package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StaticTimeBasedConfigurationService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.SurgeComputationStaticTimeBasedConfigurationManagementApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.StaticTimeBasedConfigurationMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.utilities.RequestUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for managing surge computation time-based static configurations. This controller
 * implements the SurgeComputationStaticTimeBasedConfigurationManagementApi interface and delegates
 * business logic to the SurgeComputationTimeBasedStaticConfigurationService.
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class StaticTimeBasedConfigurationManagementController
    implements SurgeComputationStaticTimeBasedConfigurationManagementApi {

  private final StaticTimeBasedConfigurationService staticTimeBasedConfigurationService;
  private final StaticTimeBasedConfigurationMapper staticTimeBasedConfigurationMapper;

  @Override
  public ResponseEntity<StaticBasedConfigurationEffectiveCheckResponse>
      staticTimeBasedConfigurationEffectiveCheck() {
    StaticBasedConfigurationEffectiveCheckEntity entity =
        staticTimeBasedConfigurationService.effectiveCheck();
    StaticBasedConfigurationEffectiveCheckResponse response =
        staticTimeBasedConfigurationMapper.mapEffectiveCheckEntityToDto(entity);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StaticBasedConfigurationVersionListResponse>
      getStaticTimeBasedConfigurationVersions() {
    List<StaticBasedConfigurationVersionEntity> versions =
        staticTimeBasedConfigurationService.getStaticTimeBasedConfigurationVersions();

    StaticBasedConfigurationVersionListResponse response =
        new StaticBasedConfigurationVersionListResponse();
    response.setData(staticTimeBasedConfigurationMapper.mapVersionEntityToDto(versions));
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StaticTimeBasedConfigurationCreateResponse>
      batchCreateStaticTimeBasedConfiguration(List<StaticTimeBasedConfigurationRequest> request) {
    // Check the user ID from the request header, when operate database will auto get from
    // SpringSecurityAuditorAware
    RequestUtils.getUserIdFromHeader();

    List<StaticTimeBasedConfigurationEntity> requestEntities =
        staticTimeBasedConfigurationMapper.mapRequestToEntity(request);

    List<StaticTimeBasedConfigurationEntity> createdEntities =
        staticTimeBasedConfigurationService.batchCreateStaticTimeBasedConfiguration(
            requestEntities);

    StaticTimeBasedConfigurationCreateResponse response =
        new StaticTimeBasedConfigurationCreateResponse();
    response.setData(staticTimeBasedConfigurationMapper.mapEntityToDto(createdEntities));
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<StaticTimeBasedConfigurationListResponse> getStaticTimeBasedConfigurations(
      String version) {
    List<StaticTimeBasedConfigurationEntity> entities =
        staticTimeBasedConfigurationService.getStaticTimeBasedConfigurations(version);

    // Change mapEntityListToDto to mapEntityToDto which is the correct method name in the mapper
    List<StaticTimeBasedConfiguration> models =
        staticTimeBasedConfigurationMapper.mapEntityToDto(entities);

    StaticTimeBasedConfigurationListResponse response =
        new StaticTimeBasedConfigurationListResponse();
    response.setData(models);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<StaticTimeBasedConfigurationResponse> getStaticTimeBasedConfigurationById(
      Long id) {
    StaticTimeBasedConfigurationEntity entity =
        staticTimeBasedConfigurationService.getSurgeComputationTimeBasedStaticConfigurationById(id);

    if (entity == null) {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    StaticTimeBasedConfigurationResponse response = createResponse(entity);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<StaticTimeBasedConfigurationResponse> updateStaticTimeBasedConfiguration(
      Long id, StaticTimeBasedConfigurationRequest request) {

    String userId = RequestUtils.getUserIdFromHeader();

    StaticTimeBasedConfigurationEntity requestEntity =
        staticTimeBasedConfigurationMapper.mapRequestToEntity(request);

    StaticTimeBasedConfigurationEntity updatedEntity =
        staticTimeBasedConfigurationService.updateSurgeComputationTimeBasedStaticConfiguration(
            id, requestEntity, userId);

    if (updatedEntity == null) {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    StaticTimeBasedConfigurationResponse response = createResponse(updatedEntity);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  public ResponseEntity<Void> deleteStaticTimeBasedConfiguration(Long id) {
    String userId = RequestUtils.getUserIdFromHeader();

    boolean deleted =
        staticTimeBasedConfigurationService.deleteSurgeComputationTimeBasedStaticConfiguration(
            id, userId);

    if (deleted) {
      return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    } else {
      return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
  }

  private StaticTimeBasedConfigurationResponse createResponse(
      StaticTimeBasedConfigurationEntity entity) {
    StaticTimeBasedConfiguration model = staticTimeBasedConfigurationMapper.mapEntityToDto(entity);

    StaticTimeBasedConfigurationResponse response = new StaticTimeBasedConfigurationResponse();
    response.setData(model);
    response.setTimestamp(OffsetDateTime.now(ZoneOffset.UTC));
    response.setTraceId(UUID.randomUUID().toString().replace("-", ""));

    return response;
  }
}
