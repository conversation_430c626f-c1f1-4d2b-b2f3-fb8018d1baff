package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.surgecomputation;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.NOT_FOUND_REGION_MODEL_DISTRIBUTION;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.RegionModelDistributionRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation.RegionModelDistributionMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.RegionModelDistributionJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation.RegionModelDistributionJPARepository;
import java.text.MessageFormat;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class RegionModelDistributionRepositoryImpl implements RegionModelDistributionRepository {

  private final RegionModelDistributionJPARepository regionModelDistributionJPARepository;
  private final RegionModelDistributionMapper regionModelDistributionMapper;

  @Override
  @Transactional(readOnly = true)
  public List<RegionModelDistributionEntity> findByRegionId(final Long regionId) {
    List<RegionModelDistributionJPA> distributions =
        regionModelDistributionJPARepository.findAllByRegionIdOrderByEffectiveFromDesc(regionId);
    return regionModelDistributionMapper.mapJpaToEntity(distributions);
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<RegionModelDistributionEntity> findById(final Long id) {
    Optional<RegionModelDistributionJPA> distribution =
        regionModelDistributionJPARepository.findById(id);
    return distribution.map(regionModelDistributionMapper::mapJpaToEntity);
  }

  @Override
  @Transactional
  public RegionModelDistributionEntity saveOrUpdate(final RegionModelDistributionEntity entity) {
    RegionModelDistributionJPA distribution;
    if (entity.getId() == null) {
      // create
      /*
       * When process insert, there will be a function populate_effective_to_before_insert_model_distribution
       * to check if there is a previous version with effectiveTo as null and the current effectiveFrom
       * is later than the previous effectiveFrom. If so, update the previous effectiveTo to the moment
       * before the current effectiveFrom.
       */
      distribution = regionModelDistributionMapper.mapEntityToJpa(entity);
    } else {
      // update
      distribution = getAndMappingDistribution(entity);
    }

    RegionModelDistributionJPA saved = regionModelDistributionJPARepository.save(distribution);
    return regionModelDistributionMapper.mapJpaToEntity(saved);
  }

  @Override
  @Transactional
  public void delete(final RegionModelDistributionEntity entity) {
    RegionModelDistributionJPA distribution = regionModelDistributionMapper.mapEntityToJpa(entity);
    regionModelDistributionJPARepository.delete(distribution);
  }

  @Override
  @Transactional
  public void save(final RegionModelDistributionEntity entity) {
    RegionModelDistributionJPA distribution = regionModelDistributionMapper.mapEntityToJpa(entity);
    regionModelDistributionJPARepository.save(distribution);
  }

  @Override
  @Transactional(readOnly = true)
  public long countByModelId(final Long modelId) {
    return regionModelDistributionJPARepository.countByModelId(modelId);
  }

  private RegionModelDistributionJPA getAndMappingDistribution(
      final RegionModelDistributionEntity entity) {
    RegionModelDistributionJPA distribution;
    Optional<RegionModelDistributionJPA> distributionOptional =
        regionModelDistributionJPARepository.findById(entity.getId());

    distribution =
        distributionOptional.orElseThrow(
            () -> {
              log.error(
                  "[saveOrUpdate] Not found region model distribution for id {}", entity.getId());
              return new NotFoundException(
                  MessageFormat.format(
                      ErrorEnum.NOT_FOUND_REGION_MODEL_DISTRIBUTION.getMessage(), entity.getId()),
                  NOT_FOUND_REGION_MODEL_DISTRIBUTION.getErrorCode());
            });
    regionModelDistributionMapper.mapEntityToJpa(distribution, entity);
    return distribution;
  }
}
