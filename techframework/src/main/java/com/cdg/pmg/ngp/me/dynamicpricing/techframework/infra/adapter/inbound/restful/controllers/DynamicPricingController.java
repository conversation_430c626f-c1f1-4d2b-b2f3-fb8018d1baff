package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.controllers;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.INVALID_TRIP_ID;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FareDetail;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DemandSupplyService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.DynamicPricingRegionBasedService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.ModelService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.RegionModelDistributionService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3RegionComputeResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.GeneratedRouteEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownRequestEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.SearchFareBreakdownResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.ValidateFareEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.DynamicPricingSurgeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.GetFareCountEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.api.DynamicPricingControllerApi;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.dtos.FareDetailInboundResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.AdditionalChargeFeeMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.DynamicPricingMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/** The type Home controller. */
@RestController
@RequiredArgsConstructor
@Slf4j
public class DynamicPricingController implements DynamicPricingControllerApi {

  private final DynamicPricingService dynamicPricingService;
  private final DemandSupplyService demandSupplyService;
  private final DynamicPricingMapper mapper;
  private final RequestCounterService requestCounterService;
  private final AddressService addressService;
  private final DynamicPricingRegionBasedService dynamicPricingRegionBasedService;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;
  private final RegionModelDistributionService regionModelDistributionService;
  private final ModelService modelService;

  private static final String UPDATED_DYNP_SURGE_SUCCESS_MSG =
      "Update demand supply surge successfully";

  private static final String UPDATED_DYNP_SURGE_NGP_SUCCESS_MSG =
      "Update demand supply surge ngp successfully";

  @Override
  public ResponseEntity<GetEstimatedFareResponse> getMultiFare(
      GetEstimatedFareInboundRequest getEstimatedFareInboundRequest) {
    log.info("[getMultiFare] Start getMultiFare - request: {}", getEstimatedFareInboundRequest);

    Instant apiRequestTime = Instant.now();
    MultiFareRequestQuery requestQuery =
        mapper.mapToEstFareRequestQuery(getEstimatedFareInboundRequest);
    // Set the request time equal to this API request time. This is used for surge computation.
    requestQuery.setRequestTime(apiRequestTime);

    // Resolve the region id by pickup address coordinate, maybe empty if no region found.
    Optional<H3RegionComputeResponse> region = resolveRegionId(getEstimatedFareInboundRequest);
    if (region.isEmpty()) {
      log.warn("[getMultiFare] No region found for request: {}", requestQuery);
    }

    // Get multi fare by region based or zoen based.
    MultiFareResponse multiFareResponse = getMultiFareByStrategy(requestQuery, region);

    // Only record request count when region is exists, because the request count is based on
    // region.
    region.ifPresent(r -> recordFareRequest(requestQuery, r, apiRequestTime));

    final GetEstimatedFareResponse response = new GetEstimatedFareResponse();
    response.data(mapper.mapToGetEstimatedFareInboundResponse(multiFareResponse));

    return ResponseEntity.ok(response);
  }

  @RequestMapping(
      method = RequestMethod.POST,
      value = "/v1.0/pricing/multi-fare-detail",
      produces = {"application/json"},
      consumes = {"application/json"})
  public ResponseEntity<FareDetailInboundResponse> getMultiFareDetail(
      @RequestBody MultiFareRequestQuery multiFareRequestQuery) {
    log.info("CONTROLLER - Start getMultiFareDetail - request: {}", multiFareRequestQuery);

    final FareDetail fareDetail = dynamicPricingService.getMultiFareDetail(multiFareRequestQuery);
    final FareDetailInboundResponse fareDetailInboundResponse =
        mapper.mapToFareDetailInboundResponse(fareDetail);
    return ResponseEntity.ok(fareDetailInboundResponse);
  }

  @Override
  public ResponseEntity<GetEstimatedFareResponse> getMultiFareById(String fareId) {
    log.info("CONTROLLER - Start get multi-fare by requested fare ID: {}", fareId);
    final MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFareByFareId(fareId);
    final GetEstimatedFareResponse response = new GetEstimatedFareResponse();
    response.data(mapper.mapToGetEstimatedFareInboundResponse(multiFareResponse));
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<StoreFareBreakdownInboundResponse> storeFareBreakdown(
      StoreFareBreakdownInboundRequest storeFareBreakdownInboundRequest) {
    final StoreFareBreakdownCommandRequest request =
        mapper.mapToStoreFareBreakdownRequestQuery(storeFareBreakdownInboundRequest);

    final StoreFareBreakdownCommandResponse queryResponse =
        dynamicPricingService.storeFareBreakdownDetail(request);

    final StoreFareBreakdownInboundResponse response = new StoreFareBreakdownInboundResponse();
    response.data(mapper.mapToStoreFareBreakdownInboundResponseData(queryResponse));

    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<ValidateFareResponse> verifyFare(ValidateFareRequest verifyFareRequest) {
    log.info("CONTROLLER - Start to validate fare - request: {}", verifyFareRequest);
    final ValidateFareEntity request = mapper.mapToValidateFareEntity(verifyFareRequest);

    final ValidateFareResponseData data = new ValidateFareResponseData();
    data.setIsValidFare(dynamicPricingService.validateFare(request));

    final ValidateFareResponse response = new ValidateFareResponse();
    response.setData(data);

    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<GetGeneratedRouteResponse> getGeneratedRoute(String tripId) {
    log.info("Dynamic Pricing Controller - Start get generated route - tripId: {}", tripId);
    if (Objects.isNull(tripId)) {
      log.error("Invalid trip id");
      throw new BadRequestException(INVALID_TRIP_ID.getMessage(), INVALID_TRIP_ID.getErrorCode());
    }
    GeneratedRouteEntity generatedRouteEntity =
        dynamicPricingService.getGeneratedRouteByTripId(tripId);
    GeneratedRoute data = mapper.mapGeneratedRouteEntityToGeneratedRoute(generatedRouteEntity);
    GetGeneratedRouteResponse response = new GetGeneratedRouteResponse();
    response.setData(data);
    log.info("Dynamic Pricing Controller - End get generated route - tripId: {}", tripId);
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<UpdateDemandSuccessResponse> updateDynamicSurge() {
    log.info("CONTROLLER - Start to update dynamic surge");
    demandSupplyService.calculateDemandSupplySurge();

    final UpdateDemandSuccessResponse response = new UpdateDemandSuccessResponse();
    response.setData(UPDATED_DYNP_SURGE_SUCCESS_MSG);

    log.info("Dynamic Pricing Controller - End update dynamic surge");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<SearchFareBreakdownInboundResponse> searchFareBreakdown(
      SearchFareBreakdownInboundRequest searchFareBreakdownRequest) {
    log.info("CONTROLLER - Start to search fare breakdown");
    final SearchFareBreakdownRequestEntity request =
        mapper.mapToSearchBreakdownRequest(searchFareBreakdownRequest);
    final SearchFareBreakdownResponse searchFareBreakdownResponse =
        dynamicPricingService.searchFareBreakdown(request);
    final SearchFareBreakdownInboundResponse response =
        mapper.mapToSearchFareBreakdownResponse(searchFareBreakdownResponse);
    log.info("Dynamic Pricing Controller - End search fare breakdown");
    return ResponseEntity.ok(response);
  }

  /**
   * Invoked by the AWS EventBridge Scheduler to update dynamic surge values. This method triggers
   * the recalculation of dynamic surge factors via the {@link
   * DemandSupplyService#calculateDemandSupplySurgeNgp()} method.
   *
   * <p>Upon successful completion, it returns a response entity containing a success message,
   * indicating that the dynamic surge update for NGP has been completed.
   *
   * @return a {@link ResponseEntity} containing an {@link UpdateDemandSuccessResponse} with the
   *     success message after successfully updating the NGP dynamic surge.
   */
  @Override
  public ResponseEntity<UpdateDemandSuccessResponse> updateDynamicSurgeV2() {
    log.info("Start to update dynamic surge NGP-- V2");
    demandSupplyService.calculateDemandSupplySurgeNgp();

    final UpdateDemandSuccessResponse response = new UpdateDemandSuccessResponse();
    response.setData(UPDATED_DYNP_SURGE_NGP_SUCCESS_MSG);

    log.info("Dynamic Pricing Controller - End update dynamic surge NGP");
    return ResponseEntity.ok(response);
  }

  @Override
  public ResponseEntity<AdditionalChargeFeesResponse> getAdditionalChargeFeesByCondition(
      String fareId, Integer vehTypeId, String productTypeId) {

    List<AdditionalChargeFeeData> additionalChargeFeeDataList =
        dynamicPricingService.getAdditionalChargeFeesByCondition(fareId, vehTypeId, productTypeId);

    if (ObjectUtils.isEmpty(additionalChargeFeeDataList)) {
      log.warn(
          "Not found additionalChargeFees by condition,fareId={},vehTypeId={},productTypeId={} .",
          fareId,
          vehTypeId,
          productTypeId);
      return ResponseEntity.notFound().build();
    } else {

      List<AdditionalChargeFee> additionalChargeFeeList =
          AdditionalChargeFeeMapper.mapToAdditionalChargeFeeDataList(additionalChargeFeeDataList);

      AdditionalChargeFeesResponse additionalChargeFeesResponse =
          new AdditionalChargeFeesResponse();
      additionalChargeFeesResponse.setData(additionalChargeFeeList);

      log.info(
          "The return additional Charge Fees, additionalChargeFeesResponse={} .",
          additionalChargeFeesResponse);

      return ResponseEntity.ok(additionalChargeFeesResponse);
    }
  }

  private MultiFareResponse getMultiFareByStrategy(
      MultiFareRequestQuery requestQuery, Optional<H3RegionComputeResponse> region) {

    // Controls the percentage for accessing the Regions and Zones calculation result.
    DynamicPricingSurgeConfig surgeConfig =
        configurationServiceOutboundPort.getDynamicPricingSurgeConfig();

    // Set the region id and version to the request query. Because whatever region based or zone
    // based all need the region id and version, so we set it here.
    if (region.isPresent()) {
      requestQuery.setRegionId(region.get().getRegionId());
      requestQuery.setRegionVersion(region.get().getRegionVersion());
    }

    if (surgeConfig.isRandomToRegion()) {
      // Calculate by Region Based
      return getRegionBasedFare(requestQuery);
    } else {
      // Calculate by Zone Based
      return getZoneBasedFare(requestQuery);
    }
  }

  private MultiFareResponse getRegionBasedFare(MultiFareRequestQuery requestQuery) {
    log.info("[getMultiFare] getMultiFare based on region");

    Long regionId = null;
    Long modelId = null;
    String modelName = null;

    if (requestQuery.getRegionId() != null) {
      ModelPercentage model = selectModelByCumulativeWeight(requestQuery);
      regionId = requestQuery.getRegionId();
      modelId = model.getModelId();
      modelName = model.getModelName();
    }

    requestQuery.setAreaType(SurgeAreaTypeEnum.REGION);
    requestQuery.setModelId(modelId);
    requestQuery.setModelName(modelName);

    MultiFareResponse response = dynamicPricingRegionBasedService.getMultiFare(requestQuery);
    response.setModelId(modelId);
    response.setModelName(modelName);
    response.setAreaType(SurgeAreaTypeEnum.REGION.getValue());
    response.setRegionId(regionId);

    return response;
  }

  private MultiFareResponse getZoneBasedFare(MultiFareRequestQuery requestQuery) {
    log.info("[getMultiFare] getMultiFare based on zone");

    requestQuery.setAreaType(SurgeAreaTypeEnum.ZONE);

    MultiFareResponse response = dynamicPricingService.getMultiFare(requestQuery);
    response.setAreaType(SurgeAreaTypeEnum.ZONE.getValue());

    return response;
  }

  private void recordFareRequest(
      MultiFareRequestQuery requestQuery, H3RegionComputeResponse region, Instant requestTime) {

    GetFareCountEntity getFareCountEntity =
        new GetFareCountEntity(
            requestQuery.getAreaType(),
            region.getRegionId(),
            region.getRegionVersion(),
            requestQuery.getModelId(),
            requestTime);

    log.info("[getMultiFare] recordFareRequest - getFareCountEntity: {}", getFareCountEntity);
    requestCounterService.recordEndpointRequest(
        RequestCountConstant.MULTI_FARE, getFareCountEntity);
  }

  private ModelPercentage selectModelByCumulativeWeight(final MultiFareRequestQuery requestQuery) {
    List<ModelPercentage> models = getModelPercentages(requestQuery.getRegionId());

    ModelPercentage modelPercentage = null;

    double randomValue = ThreadLocalRandom.current().nextDouble(1, 101);
    double cumulativeWeight = 0.0;
    for (ModelPercentage model : models) {
      cumulativeWeight += model.getPercentage().doubleValue();
      if (randomValue <= cumulativeWeight) {
        modelPercentage = model;
        break;
      }
    }

    if (modelPercentage == null) {
      // Theoretically, will never enter here.
      log.warn("[selectModelByCumulativeWeight] use default first model");
      modelPercentage = models.get(0);
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[selectModelByCumulativeWeight] mobile: {}, pickupAddressRef: {}, pickupAddressLat: {}, pickupAddressLng: {}, regionId: {}, regionVersion: {}, modelRandomValue: {}, modelId: {}, modelName: {}",
          requestQuery.getMobile(),
          requestQuery.getPickupAddressRef(),
          requestQuery.getPickupAddressLat(),
          requestQuery.getPickupAddressLng(),
          requestQuery.getRegionId(),
          requestQuery.getRegionVersion(),
          randomValue,
          modelPercentage.getModelId(),
          modelPercentage.getModelName());
    }

    String modelName = modelService.getSurgeComputationModelName(modelPercentage.getModelId());
    modelPercentage.setModelName(modelName);

    return modelPercentage;
  }

  private @NotNull List<ModelPercentage> getModelPercentages(final Long regionId) {
    Optional<RegionModelDistributionEntity> distributionOptional =
        regionModelDistributionService.getEffectiveRegionModelDistribution(regionId);

    // If no effective distribution found, will use all models with 50% weight
    List<ModelPercentage> models =
        distributionOptional
            .map(RegionModelDistributionEntity::getModels)
            .orElseGet(
                () -> {
                  log.warn(
                      "[selectModelByCumulativeWeight] No effective region model distribution found, regionId: {}",
                      regionId);
                  return modelService.getAllSurgeComputationModels().stream()
                      .map(
                          model ->
                              new ModelPercentage(
                                  model.getId(), BigDecimal.valueOf(50), model.getModelName()))
                      .toList();
                });

    // If all model allocation is 0%, will use all models with 50% weight
    BigDecimal totalPercentage =
        models.stream()
            .map(ModelPercentage::getPercentage)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    if (totalPercentage.compareTo(BigDecimal.ZERO) == 0) {
      log.warn("[selectModelByCumulativeWeight] All model allocation is 0, regionId: {}", regionId);
      models.forEach(model -> model.setPercentage(BigDecimal.valueOf(50)));
    }
    return models;
  }

  private Optional<H3RegionComputeResponse> resolveRegionId(
      final GetEstimatedFareInboundRequest getEstimatedFareInboundRequest) {
    H3RegionComputeRequest request =
        mapper.mapToH3RegionComputeRequest(getEstimatedFareInboundRequest);
    return addressService.resolveH3Region(request);
  }
}
