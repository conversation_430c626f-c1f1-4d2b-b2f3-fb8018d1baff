package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.MlGetFareRequestAggStatsJPA;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for {@link MlGetFareRequestAggStatsJPA} entity. Provides methods to interact
 * with the ml_get_fare_request_agg_stats table.
 */
@Repository
public interface MlGetFareRequestAggStatsJPARepository
    extends JpaRepository<MlGetFareRequestAggStatsJPA, Long> {}
