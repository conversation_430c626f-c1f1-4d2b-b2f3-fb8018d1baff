package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.StaticTimeBasedConfigurationJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.projection.StaticBasedConfigurationVersionProjection;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * Maps between SurgeComputationTimeBasedStaticConfigurationJPA entity,
 * SurgeComputationTimeBasedStaticConfigurationEntity domain entity, and
 * SurgeComputationTimeBasedStaticConfiguration API model.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StaticTimeBasedConfigurationMapper {

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationJPA to
   * SurgeComputationTimeBasedStaticConfigurationEntity.
   *
   * @param source the JPA entity
   * @return the domain entity
   */
  StaticTimeBasedConfigurationEntity mapJpaToEntity(StaticTimeBasedConfigurationJPA source);

  /**
   * Maps a list of SurgeComputationTimeBasedStaticConfigurationJPA to a list of
   * SurgeComputationTimeBasedStaticConfigurationEntity. This method is implemented as a default
   * method to avoid ambiguity with mapJpaToEntityWithAppliedHours.
   *
   * @param sources the list of JPA entities
   * @return the list of domain entities
   */
  default List<StaticTimeBasedConfigurationEntity> mapJpaToEntity(
      List<StaticTimeBasedConfigurationJPA> sources) {
    if (sources == null) {
      return null;
    }

    return sources.stream().map(this::mapJpaToEntity).toList();
  }

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationEntity to
   * SurgeComputationTimeBasedStaticConfigurationJPA.
   *
   * @param source the domain entity
   * @return the JPA entity
   */
  @Mapping(source = "appliedHours", target = "appliedHours")
  StaticTimeBasedConfigurationJPA mapEntityToJpa(StaticTimeBasedConfigurationEntity source);

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationEntity to
   * SurgeComputationTimeBasedStaticConfigurationJPA.
   *
   * @param source a list of the domain entity
   * @return a list of the JPA entity
   */
  List<StaticTimeBasedConfigurationJPA> mapEntityToJpa(
      List<StaticTimeBasedConfigurationEntity> source);

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationEntity to
   * SurgeComputationTimeBasedStaticConfiguration.
   *
   * @param source the domain entity
   * @return the API model
   */
  StaticTimeBasedConfiguration mapEntityToDto(StaticTimeBasedConfigurationEntity source);

  /**
   * Maps a list of SurgeComputationTimeBasedStaticConfigurationEntity to a list of
   * SurgeComputationTimeBasedStaticConfiguration.
   *
   * @param sources the list of domain entities
   * @return the list of API models
   */
  default List<StaticTimeBasedConfiguration> mapEntityToDto(
      List<StaticTimeBasedConfigurationEntity> sources) {
    if (sources == null) {
      return null;
    }

    return sources.stream().map(this::mapEntityToDto).toList();
  }

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationEntity to the existing
   * SurgeComputationTimeBasedStaticConfigurationJPA.
   *
   * @param jpa the target JPA entity
   * @param entity the source domain entity
   */
  @Mapping(source = "appliedHours", target = "appliedHours")
  void mapEntityToJpa(
      @MappingTarget StaticTimeBasedConfigurationJPA jpa,
      StaticTimeBasedConfigurationEntity entity);

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationEntity to the existing
   * SurgeComputationTimeBasedStaticConfigurationJPA with audit fields.
   *
   * @param jpa the target JPA entity
   * @param entity the source domain entity
   */
  @Mapping(source = "appliedHours", target = "appliedHours")
  void mapEntityToJpaWithAuditUpdate(
      @MappingTarget StaticTimeBasedConfigurationJPA jpa,
      StaticTimeBasedConfigurationEntity entity);

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationRequest to
   * SurgeComputationTimeBasedStaticConfigurationEntity.
   *
   * @param source the API request model
   * @return the domain entity
   */
  StaticTimeBasedConfigurationEntity mapRequestToEntity(StaticTimeBasedConfigurationRequest source);

  /**
   * Maps SurgeComputationTimeBasedStaticConfigurationRequest to
   * SurgeComputationTimeBasedStaticConfigurationEntity.
   *
   * @param source a list of the API request model
   * @return a list of the domain entity
   */
  List<StaticTimeBasedConfigurationEntity> mapRequestToEntity(
      List<StaticTimeBasedConfigurationRequest> source);

  /**
   * Custom mapping for applied hours from JPA to entity.
   *
   * @param appliedHours the list of applied hours from JPA
   * @return the list of applied hours for entity
   */
  default List<StaticTimeBasedConfigurationEntity.AppliedHour> mapAppliedHoursJpaToEntity(
      List<StaticTimeBasedConfigurationJPA.AppliedHour> appliedHours) {
    if (appliedHours == null) {
      return new ArrayList<>();
    }
    return appliedHours.stream().map(this::mapAppliedHourJpaToEntity).collect(Collectors.toList());
  }

  /**
   * Custom mapping for applied hours from entity to JPA.
   *
   * @param appliedHours the list of applied hours from entity
   * @return the list of applied hours for JPA
   */
  default List<StaticTimeBasedConfigurationJPA.AppliedHour> mapAppliedHoursEntityToJpa(
      List<StaticTimeBasedConfigurationEntity.AppliedHour> appliedHours) {
    if (appliedHours == null) {
      return new ArrayList<>();
    }
    return appliedHours.stream().map(this::mapAppliedHourEntityToJpa).collect(Collectors.toList());
  }

  /**
   * Converts Instant to OffsetDateTime.
   *
   * @param instant the Instant to convert
   * @return the equivalent OffsetDateTime
   */
  default OffsetDateTime map(Instant instant) {
    return instant != null ? instant.atOffset(ZoneOffset.UTC) : null;
  }

  /**
   * Converts OffsetDateTime to Instant.
   *
   * @param offsetDateTime the OffsetDateTime to convert
   * @return the equivalent Instant
   */
  default Instant map(OffsetDateTime offsetDateTime) {
    return offsetDateTime != null ? offsetDateTime.toInstant() : null;
  }

  /**
   * Maps a DayOfWeekEnum to a string for the API model.
   *
   * @param dayOfWeek the DayOfWeekEnum
   * @return the string day of week
   */
  default String mapDayOfWeekEnumToString(DayOfWeekEnum dayOfWeek) {
    return dayOfWeek != null ? dayOfWeek.getValue() : null;
  }

  /**
   * Maps a string day of week to a DayOfWeekEnum for the domain entity.
   *
   * @param dayOfWeek the string day of week
   * @return the DayOfWeekEnum
   */
  default DayOfWeekEnum mapStringToDayOfWeekEnum(String dayOfWeek) {
    return DayOfWeekEnum.getByValue(dayOfWeek);
  }

  /**
   * Maps a StaticTimeBasedConfigurationJPA.AppliedHour to a
   * StaticTimeBasedConfigurationEntity.AppliedHour, converting the string day of week to a
   * DayOfWeekEnum.
   *
   * @param source the JPA entity applied hour
   * @return the domain entity applied hour
   */
  default StaticTimeBasedConfigurationEntity.AppliedHour mapAppliedHourJpaToEntity(
      StaticTimeBasedConfigurationJPA.AppliedHour source) {
    if (source == null) {
      return null;
    }

    return StaticTimeBasedConfigurationEntity.AppliedHour.builder()
        .dayOfWeek(mapStringToDayOfWeekEnum(source.getDayOfWeek()))
        .hourOfDay(source.getHourOfDay())
        .value(source.getValue())
        .build();
  }

  /**
   * Maps a StaticTimeBasedConfigurationEntity.AppliedHour to a
   * StaticTimeBasedConfigurationJPA.AppliedHour, converting the DayOfWeekEnum to a string.
   *
   * @param source the domain entity applied hour
   * @return the JPA entity applied hour
   */
  default StaticTimeBasedConfigurationJPA.AppliedHour mapAppliedHourEntityToJpa(
      StaticTimeBasedConfigurationEntity.AppliedHour source) {
    if (source == null) {
      return null;
    }

    return new StaticTimeBasedConfigurationJPA.AppliedHour(
        mapDayOfWeekEnumToString(source.getDayOfWeek()), source.getHourOfDay(), source.getValue());
  }

  /**
   * Custom implementation for mapping from JPA to entity, handling the applied hours.
   *
   * @param source the JPA entity
   * @return the domain entity
   */
  default StaticTimeBasedConfigurationEntity mapJpaToEntityWithAppliedHours(
      StaticTimeBasedConfigurationJPA source) {
    if (source == null) {
      return null;
    }

    StaticTimeBasedConfigurationEntity entity = mapJpaToEntity(source);
    entity.setAppliedHours(mapAppliedHoursJpaToEntity(source.getAppliedHours()));
    return entity;
  }

  /**
   * Custom implementation for mapping a list of JPA entities to a list of domain entities, handling
   * the applied hours.
   *
   * @param sources the list of JPA entities
   * @return the list of domain entities
   */
  default List<StaticTimeBasedConfigurationEntity> mapJpaToEntityWithAppliedHoursList(
      List<StaticTimeBasedConfigurationJPA> sources) {
    if (sources == null) {
      return null;
    }

    return sources.stream().map(this::mapJpaToEntityWithAppliedHours).toList();
  }

  List<StaticBasedConfigurationVersion> mapVersionEntityToDto(
      List<StaticBasedConfigurationVersionEntity> versions);

  List<StaticBasedConfigurationVersionEntity> mapVersionProjectionToEntity(
      List<StaticBasedConfigurationVersionProjection> allVersionsDesc);

  StaticBasedConfigurationEffectiveCheckResponse mapEffectiveCheckEntityToDto(
      StaticBasedConfigurationEffectiveCheckEntity entity);
}
