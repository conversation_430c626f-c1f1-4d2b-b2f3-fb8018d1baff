package com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.inbound.restful.mapper.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.infra.adapter.outbound.integration.repositories.springdatajpa.entities.surgecomputation.RegionModelDistributionJPA;
import com.cdg.pmg.ngp.me.dynamicpricing.techframework.model.*;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface RegionModelDistributionMapper {

  RegionModelDistributionEntity mapToRegionModelDistributionEntity(
      CreateOrUpdateRegionModelDistributionRequest request);

  RegionModelDistributionEntity mapJpaToEntity(RegionModelDistributionJPA jpa);

  List<RegionModelDistributionEntity> mapJpaToEntity(List<RegionModelDistributionJPA> jpa);

  RegionModelDistributionJPA mapEntityToJpa(RegionModelDistributionEntity entity);
  
  List<RegionModelDistributionJPA> mapEntityToJpa(List<RegionModelDistributionEntity> entities);

  @Mapping(target = "id", ignore = true)
  void mapEntityToJpa(
      @MappingTarget RegionModelDistributionJPA jpa, RegionModelDistributionEntity entity);

  default OffsetDateTime map(Instant instant) {
    return instant != null ? instant.atOffset(ZoneOffset.UTC) : null;
  }

  default Instant map(OffsetDateTime offsetDateTime) {
    return offsetDateTime != null ? offsetDateTime.toInstant() : null;
  }

  default List<GetRegionModelDistributionResponse> entitiesToResponse(
      List<RegionModelDistributionEntity> entities) {
    return entities.stream()
        .collect(Collectors.groupingBy(RegionModelDistributionEntity::getRegionId))
        .entrySet()
        .stream()
        .map(
            entry -> {
              GetRegionModelDistributionResponse response =
                  new GetRegionModelDistributionResponse();
              response.setRegionId(entry.getKey());
              response.setModelDistributionVersions(
                  mapToModelDistributionVersions(entry.getValue()));
              return response;
            })
        .toList();
  }

  @Mapping(target = "models", expression = "java(sortModelsByModelId(entity.getModels()))")
  List<GetRegionModelDistributionResponseModelDistributionVersionsInner>
      mapToModelDistributionVersions(List<RegionModelDistributionEntity> regionModelDistributions);

  default List<GetRegionModelDistributionResponseModelDistributionVersionsInnerModelsInner>
      sortModelsByModelId(List<ModelPercentage> models) {
    if (models == null) {
      return null;
    }

    return models.stream()
        .sorted(Comparator.comparing(ModelPercentage::getModelId))
        .map(this::modelPercentageToModelsInner)
        .collect(Collectors.toList());
  }

  GetRegionModelDistributionResponseModelDistributionVersionsInnerModelsInner
      modelPercentageToModelsInner(ModelPercentage modelPercentage);

  List<RegionModelDistributionEntity> mapToRegionModelDistributionEntities(
      List<RegionModelDistribution> regionModelDistributions);
}
