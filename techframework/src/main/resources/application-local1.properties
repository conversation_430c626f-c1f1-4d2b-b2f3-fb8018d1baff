spring.liquibase.enabled=true

# Database config
spring.datasource.url=*********************************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect


spring.datasource.writer.url=*********************************************************************
spring.datasource.writer.username=postgres
spring.datasource.writer.password=postgres
spring.datasource.writer.driver-class-name=org.postgresql.Driver

# Reader database configuration
spring.datasource.reader.url=*********************************************************************
spring.datasource.reader.username=postgres
spring.datasource.reader.password=postgres
spring.datasource.reader.driver-class-name=org.postgresql.Driver


# Redis config
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.username=
spring.data.redis.password=
spring.data.redis.ssl.enabled=false

# Kafka Config
spring.kafka.bootstrap-servers=localhost:9200

#CMS Config
spring.cloud.config.enabled=false

# RELEASE VERSION
dps.system.param.applicationRelease=2

# Feign Client
openfeign.addressClient.name=addressClient
spring.cloud.openfeign.client.config.addressClient.url=http://localhost:17004
openfeign.fareClient.name=fareClient
spring.cloud.openfeign.client.config.fareClient.url=https://mob-me-r2fare-app.apps.sg.uat1.zig.systems
openfeign.fleetAnalyticClient.name=fleetAnalyticClient
spring.cloud.openfeign.client.config.fleetAnalyticClient.url=https://mob-me-r2fleetanalytic-app.apps.sg.uat1.zig.systems
openfeign.weatherRetrievalClient.name=weatherRetrievalClient
spring.cloud.openfeign.client.config.weatherRetrievalClient.url=https://mob-me-weather-retrieval-app.apps.sg.sit.zig.systems