package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.LiveStandardInputProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.StaticRegionBasedConfigurationProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.impl.StaticTimeBasedConfigurationProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.DayOfWeekEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.MappingTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * Factory class for creating configuration data providers.
 *
 * @see ConfigurationDataProvider
 * @see MappingTypeEnum
 */
@Slf4j
public class ConfigurationProviderFactory {
  private static final String DEFAULT_TIME_ZONE = "+00:00";
  private final Map<MappingTypeEnum, ConfigurationDataProvider> providers =
      new EnumMap<>(MappingTypeEnum.class);

  public ConfigurationProviderFactory(
      List<StaticTimeBasedConfigurationEntity> staticTimeBasedConfigs,
      Map<String, Map<Long, String>> staticRegionConfig,
      Map<Long /* regionId */, Map<String /* name */, BigDecimal>> standardInputValueMap,
      boolean isHoliday) {

    ZoneOffset timeZoneOffset = getTimeZoneOffset(staticTimeBasedConfigs);

    providers.put(
        MappingTypeEnum.STATIC_TIME_BASED_CONFIGURATION,
        new StaticTimeBasedConfigurationProvider(
            toMap(staticTimeBasedConfigs), timeZoneOffset, isHoliday));

    providers.put(
        MappingTypeEnum.STATIC_REGION_BASED_CONFIGURATION,
        new StaticRegionBasedConfigurationProvider(staticRegionConfig));

    providers.put(
        MappingTypeEnum.LIVE_STANDARD_INPUT, new LiveStandardInputProvider(standardInputValueMap));
  }

  public ConfigurationDataProvider getProvider(MappingTypeEnum type) {
    return providers.get(type);
  }

  /**
   * Get the time zone offset from the static time based configurations, if not found or invalid,
   * use default +00:00
   *
   * @param staticTimeBasedConfigs the static time based configurations
   * @return the time zone offset
   */
  private static ZoneOffset getTimeZoneOffset(
      final List<StaticTimeBasedConfigurationEntity> staticTimeBasedConfigs) {
    String timeZoneOffset = DEFAULT_TIME_ZONE;
    if (CollectionUtils.isNotEmpty(staticTimeBasedConfigs)) {
      // Here use the first one as the time zone offset, as we have checked the uniqueness before
      timeZoneOffset = staticTimeBasedConfigs.get(0).getTimeZoneOffset();
      if (timeZoneOffset == null) {
        log.warn("[calculateSurgeFactor] Time zone offset is null, using default +00:00");
        timeZoneOffset = DEFAULT_TIME_ZONE;
      }

      if (log.isDebugEnabled()) {
        log.debug(
            "[calculateSurgeFactor] Time zone offset from static time based configuration: {}",
            timeZoneOffset);
      }
    } else {
      log.warn("[calculateSurgeFactor] No time static based configuration found");
    }

    try {
      return ZoneOffset.of(timeZoneOffset);
    } catch (Exception e) {
      log.warn(
          "[calculateSurgeFactor] Invalid time zone offset: {}, using default +00:00",
          timeZoneOffset);
      return ZoneOffset.of(DEFAULT_TIME_ZONE);
    }
  }

  /**
   * Find a map of static time-based configurations by effective time range.
   *
   * <p>The result format is:
   *
   * <p>{name: {dayOfWeek: {hourOfDay: value}}}
   *
   * @param staticTimeBasedConfigs the effective time based configurations
   * @return a map of configurations
   */
  private Map<String, Map<DayOfWeekEnum, Map<Integer, String>>> toMap(
      List<StaticTimeBasedConfigurationEntity> staticTimeBasedConfigs) {
    return staticTimeBasedConfigs.stream()
        .collect(
            Collectors.toMap(
                StaticTimeBasedConfigurationEntity::getName,
                entity ->
                    entity.getAppliedHours().stream()
                        .collect(
                            Collectors.groupingBy(
                                StaticTimeBasedConfigurationEntity.AppliedHour::getDayOfWeek,
                                Collectors.toMap(
                                    StaticTimeBasedConfigurationEntity.AppliedHour::getHourOfDay,
                                    StaticTimeBasedConfigurationEntity.AppliedHour::getValue,
                                    (oldValue, newValue) -> newValue))),
                (oldValue, newValue) -> newValue));
  }
}
