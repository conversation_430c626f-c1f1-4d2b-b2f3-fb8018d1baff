@startuml multi-fare-request-count

title Multi-Fare Request Count Flow
autonumber
actor Client
participant ngp_me_dynamicpricing_svc as pricing
participant Request<PERSON>ounterAspect as aspect
participant RequestCounterServiceAdapter as counter
participant Redis

' Start Multi-Fare Request Flow
activate pricing

Client -> pricing: [POST] '/v1.0/pricing/multi-fare'

pricing -> pricing: @RecordRequestCount(endpoint = RequestCountConstant.MULTI_FARE)

' AOP intercepts the method call
aspect -> aspect: @Before recordRequest() triggered
activate aspect

aspect -> aspect: Validate recordRequestCount.enabled()

break#LightGray Recording disabled
aspect -> pricing: Skip recording (annotation enabled = false)
end

aspect -> aspect: determineEndpoint(joinPoint, recordRequestCount)

opt#LightBlue #LightGray Determine endpoint name
    alt annotation.endpoint() is not empty
        aspect -> aspect: Use annotation endpoint value\n(RequestCountConstant.MULTI_FARE)
    else annotation.endpoint() is empty
        aspect -> aspect: Try to get request URI from ServletRequestAttributes
        alt RequestURI available
            aspect -> aspect: Use request URI as endpoint
        else RequestURI not available
            aspect -> aspect: Use method name as endpoint
        end
    end
end

aspect -> aspect: Get current timestamp\nlong currentTime = System.currentTimeMillis()

aspect -> counter++: recordEndpointRequest(endpoint, currentTime)

counter -> counter: Store in local cache\nlocalRequestCache.computeIfAbsent(endpoint, k -> new ConcurrentLinkedQueue<>())\n.offer(requestTimestamp)

note right of counter
Local cache structure:
Map<String, ConcurrentLinkedQueue<Long>>
- endpoint -> queue of timestamps
end note

return Record completed

aspect -> aspect: Log debug info if enabled

deactivate aspect

pricing -> pricing: Continue with actual business logic\n(getMultiFare implementation)

pricing -> Client: Return GetEstimatedFareResponse

' Scheduled batch sync process
...

note over counter, Redis
Scheduled Task (every 30 seconds)
@Scheduled(fixedRate = 30000)
end note

counter -> counter: syncLocalDataToRedis()
activate counter

loop for each endpoint in localRequestCache
    counter -> counter: Poll all timestamps from queue

    counter -> counter: Build Redis key\nRedisKeyConstant.CACHE_KEY_GET_FARE_COUNT_PREFIX + endpoint

    counter -> counter: Create ZSet tuples\n(requestId, timestamp as score)

    counter -> Redis++: opsForZSet().add(key, tuples)
    note right of Redis
    Redis Sorted Set structure:
    Key: "fare_count:MULTI_FARE"
    Score: requestTimestamp
    Value: requestId (timestamp_uuid)
    end note
    return Batch add completed
end

deactivate counter

' Query request count process
...

note over counter, Redis
Query Request Count
getRequestCount(endpoint, startTime, endTime)
end note

counter -> Redis++: cleanExpiredRequests(key, minTimestamp)
note right of Redis
Remove expired records:
removeRangeByScore(key, -1, minTimestamp - 1)
end note
return Cleanup completed

counter -> Redis++: opsForZSet().count(key, startTime, endTime)
return Request count within time window

' End Multi-Fare Request Flow
deactivate pricing

@enduml