package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.GET_FARE_BREAK_DOWN_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.CommonConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.DynamicPricingConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.EstimateRateConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareConfigSet;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVO;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareVOPart;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.LocationSurchargeConfig;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.MultiFareResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.TierFare;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.DynpDomainMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation.MlCreateBookingRequestAggStatsMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareAdjustmentConfService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl.DynamicPricingManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl.FlatFareManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.helper.AdditionalChargeFeeConfigHelper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBreakDownRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlCreateBookingRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.CommonConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.*;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.SurgeAreaTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DynamicPricingServiceImplTest {

  @Mock private DynamicPricingService dynamicPricingService;
  @Mock private FlatFareAdjustmentConfService flatFareAdjustmentConfService;

  @Mock private AddressService addressService;

  @Mock private FlatFareManager flatFareManager;

  @Mock private CacheService cacheService;

  @Mock private DynamicPricingManager dynamicPricingManager;

  @Mock private FareService fareService;

  @Mock private DynpDomainMapper dynpDomainMapper;

  @Mock private FlatFareBreakDownRepository flatFareBreakDownRepository;

  @Mock private LocationSurchargeService locationSurchargeService;
  @Mock private DriverFeeAdditionalChargeProcessor driverFeeAdditionalChargeProcessor;
  @Mock private MlCreateBookingRequestAggStatsRepository mlCreateBookingRequestAggStatsRepository;
  @Mock private MlCreateBookingRequestAggStatsMapper mlCreateBookingRequestAggStatsMapper;

  private final String ESTIMATED_FARE_RESPONSE_KEY_CACHE_PREFIX =
      RedisKeyConstant.DYNAMIC_PRICING.concat(RedisKeyConstant.COLON);

  @BeforeEach
  void setUp() {
    driverFeeAdditionalChargeProcessor = new DriverFeeAdditionalChargeProcessor();
    dynamicPricingService =
        new DynamicPricingServiceImpl(
            flatFareAdjustmentConfService,
            addressService,
            dynpDomainMapper,
            flatFareManager,
            dynamicPricingManager,
            fareService,
            cacheService,
            flatFareBreakDownRepository,
            locationSurchargeService,
            driverFeeAdditionalChargeProcessor,
            mlCreateBookingRequestAggStatsRepository,
            mlCreateBookingRequestAggStatsMapper);
  }

  @Test
  void getEstimatedFare_limoType_success() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO flatAndMeterFareForLimo = new FlatFareVO();
    flatAndMeterFareForLimo.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    flatAndMeterFareForLimo.setTotalFare(BigDecimal.valueOf(25.0));
    flatAndMeterFareForLimo.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    flatAndMeterFareForLimo.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    flatAndMeterFareForLimo.setPdtId("FLAT-001");
    flatAndMeterFareForLimo.setCalculated(Boolean.TRUE);
    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(flatAndMeterFareForLimo);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);

    PlatformFeeResponse platformFeeResponse = getPlatformFeeResponse("YES");

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    assertTrue(
        actual.getFlatFareVOParts().size() == 2
            && Objects.equals(
                actual.getFlatFareVOParts().get(0).getTotalFare(), BigDecimal.valueOf(25.0))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareLF(), BigDecimal.valueOf(24.0))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareRT(), BigDecimal.valueOf(26.0)));
  }

  @Test
  void getEstimatedFare_normalType_success() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO flatFareForStandard = new FlatFareVO();
    flatFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    flatFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    flatFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    flatFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    flatFareForStandard.setPdtId("FLAT-001");
    flatFareForStandard.setCalculated(Boolean.TRUE);

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(
        FlatFareRequest.builder().vehTypeId(100).requestDate(reqDate).build());
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    meterFareForStandard.setMeteredBaseFare(25.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);
    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(flatFareForStandard)
        .thenReturn(meterFareForStandard);

    FlatFareVO dynamicPricingForStandard = new FlatFareVO();
    dynamicPricingForStandard.setFlatFareRequest(
        FlatFareRequest.builder().vehTypeId(100).requestDate(reqDate).build());

    doAnswer(
            invocation -> {
              Object[] args = invocation.getArguments();
              ((FlatFareVO) args[0]).setCalculated(Boolean.TRUE);
              ((FlatFareVO) args[0]).setTotalFare(BigDecimal.valueOf(25.5));
              return null;
            })
        .when(dynamicPricingManager)
        .computeTotalFare(Mockito.any(), Mockito.any());

    PlatformFeeResponse platformFeeResponse = getPlatformFeeResponse("YES");

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    assertTrue(
        actual.getFlatFareVOParts().size() == 2
            && Objects.equals(
                actual.getFlatFareVOParts().get(0).getTotalFare(), BigDecimal.valueOf(25.5))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareLF(), BigDecimal.valueOf(24.0))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareRT(), BigDecimal.valueOf(26.0)));
  }

  @Test
  void getEstimatedFare_normalTypeShowFlatFareOnlyAndWaive_success() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO flatFareForStandard = new FlatFareVO();
    flatFareForStandard.setFlatFareRequest(
        FlatFareRequest.builder().vehTypeId(100).requestDate(reqDate).build());
    flatFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    flatFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    flatFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    flatFareForStandard.setPdtId("FLAT-001");
    flatFareForStandard.setCalculated(Boolean.TRUE);

    doAnswer(
            invocation -> {
              Object[] args = invocation.getArguments();
              ((FlatFareVO) args[0]).setCalculated(Boolean.TRUE);
              ((FlatFareVO) args[0]).setTotalFare(BigDecimal.valueOf(25.5));
              ((FlatFareVO) args[0]).setFlatFareRequest(getFlatFareRequest(1, reqDate));
              return null;
            })
        .when(dynamicPricingManager)
        .computeTotalFare(Mockito.any(), Mockito.any());

    PlatformFeeResponse platformFeeResponse = getPlatformFeeResponse("WAIVE");

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    assertTrue(
        actual.getFlatFareVOParts().size() == 2
            && Objects.equals(
                actual.getFlatFareVOParts().get(0).getTotalFare(), BigDecimal.valueOf(25.5))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareLF(), BigDecimal.valueOf(0.0))
            && Objects.equals(
                actual.getFlatFareVOParts().get(1).getEstimatedFareRT(), BigDecimal.valueOf(0.0)));
  }

  @Test
  void getEstimatedFare_normalTypeShowMeterFareOnly_success() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isShowMeterFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isShowFlatFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    meterFareForStandard.setMeteredBaseFare(25.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);

    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(meterFareForStandard);

    PlatformFeeResponse platformFeeResponse = getPlatformFeeResponse("YES");

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    assertTrue(
        actual.getFlatFareVOParts().size() == 1
            && Objects.equals(
                actual.getFlatFareVOParts().get(0).getEstimatedFareLF(), BigDecimal.valueOf(24.0))
            && Objects.equals(
                actual.getFlatFareVOParts().get(0).getEstimatedFareRT(), BigDecimal.valueOf(26.0)));
  }

  @Test
  void givenEmptyData_whenValidateFare_thenReturnFalse() {
    final ValidateFareEntity request = new ValidateFareEntity();
    final String estimatedFareKeyCache =
        ESTIMATED_FARE_RESPONSE_KEY_CACHE_PREFIX
            + RedisKeyConstant.MULTI_FARE
            + RedisKeyConstant.COLON
            + request.getFareId();
    Mockito.when(cacheService.getValue(estimatedFareKeyCache, MultiFareResponse.class))
        .thenReturn(null);
    final boolean actual = dynamicPricingService.validateFare(request);
    Assertions.assertFalse(actual);
  }

  @Test
  void givenValidData_whenValidateFare_thenReturnTrue() {
    final ValidateFareEntity request = new ValidateFareEntity();
    request.setFareAmount(BigDecimal.valueOf(10));
    request.setVehTypeId(100);
    final String estimatedFareKeyCache =
        ESTIMATED_FARE_RESPONSE_KEY_CACHE_PREFIX
            + RedisKeyConstant.MULTI_FARE
            + RedisKeyConstant.COLON
            + request.getFareId();

    final MultiFareResponse response = new MultiFareResponse();
    final FlatFareVOPart flatFareVOPartResponse = new FlatFareVOPart();
    flatFareVOPartResponse.setTotalFare(BigDecimal.valueOf(10));
    flatFareVOPartResponse.setPdtId(FlatfareConstants.NORMAL_FLATFARE_PDT_ID);
    flatFareVOPartResponse.setVehTypeId(100);

    response.setFlatFareVOParts(List.of(flatFareVOPartResponse));
    Mockito.when(cacheService.getValue(estimatedFareKeyCache, MultiFareResponse.class))
        .thenReturn(response);

    final boolean actual = dynamicPricingService.validateFare(request);
    Assertions.assertTrue(actual);
  }

  @Test
  void givenTripIdIsNotNull_whenGetGeneratedRouteByTripId_thenReturnSuccessfully() {
    RouteInfo routeInfo =
        RouteInfo.builder().tripId("trip-id").routingDistance(5L).ett(1200L).build();
    Mockito.when(cacheService.getValue(Mockito.anyString(), Mockito.any())).thenReturn(routeInfo);
    GeneratedRouteEntity generatedRouteEntity =
        GeneratedRouteEntity.builder().distance(5L).duration(1200L).build();
    Mockito.when(dynpDomainMapper.mapRouteInfoToGeneratedRouteEntity(Mockito.any()))
        .thenReturn(generatedRouteEntity);
    var actual = dynamicPricingService.getGeneratedRouteByTripId("trip-id").getDuration();
    assertEquals(1200L, actual);
  }

  @Test
  void givenTripIdIsNull_whenGetGeneratedRouteByTripId_thenReturnSuccessfully() {
    GeneratedRouteEntity generatedRouteEntity =
        GeneratedRouteEntity.builder().distance(5L).duration(1200L).build();
    Mockito.when(dynpDomainMapper.mapRouteInfoToGeneratedRouteEntity(Mockito.any()))
        .thenReturn(generatedRouteEntity);
    var actual = dynamicPricingService.getGeneratedRouteByTripId("trip-id").getDuration();
    assertEquals(1200L, actual);
  }

  @Test
  void givenTripIdIsBlank_whenGetGeneratedRouteByTripId_thenThrowBadRequestException() {
    assertThrows(
        BadRequestException.class, () -> dynamicPricingService.getGeneratedRouteByTripId(""));
  }

  private MultiFareRequestQuery getEstFareRequestQuery(List<Integer> vehTypeId) {
    return MultiFareRequestQuery.builder()
        .countryCode("65")
        .bookingChannel("ANDROID")
        .mobile("91234567")
        .jobType("IMMEDIATE")
        .pickupAddressRef("154505")
        .pickupAddressLat(1.3672514)
        .pickupAddressLng(103.9040206)
        .pickupZoneId("95")
        .destAddressRef("1001")
        .destAddressLat(1.3579843)
        .destAddressLng(103.7890947)
        .destZoneId("95")
        .intermediateAddrRef("1001")
        .intermediateAddrLat(1.3579843)
        .intermediateAddrLng(103.7890947)
        .intermediateZoneId("95")
        .vehTypeIDs(vehTypeId)
        .requestTime(Instant.now())
        .areaType(SurgeAreaTypeEnum.REGION)
        .regionId(1L)
        .build();
  }

  private MultiFareRequestEntity getEstFareRequestEntity(List<Integer> vehTypeId) {
    return MultiFareRequestEntity.builder()
        .countryCode("65")
        .bookingChannel("ANDROID")
        .mobile("91234567")
        .jobType("IMMEDIATE")
        .pickupAddressRef("154505")
        .pickupAddressLat(1.3672514)
        .pickupAddressLng(103.9040206)
        .pickupZoneId("95")
        .destAddressRef("1001")
        .destAddressLat(1.3579843)
        .destAddressLng(103.7890947)
        .destZoneId("95")
        .intermediateAddrRef("1001")
        .intermediateAddrLat(1.3579843)
        .intermediateAddrLng(103.7890947)
        .intermediateZoneId("95")
        .vehTypeIDs(vehTypeId)
        .areaType(SurgeAreaTypeEnum.REGION)
        .regionId(1L)
        .build();
  }

  private FlatFareRequest getFlatFareRequest(int vehTypeId, Date reqDate) {
    return FlatFareRequest.builder()
        .countryCode("65")
        .bookingChannel("ANDROID")
        .mobileId("91234567")
        .jobType("CJ")
        .originAddressRef("154505")
        .originAddressLat(1.3672514)
        .originAddressLng(103.9040206)
        .originZoneId("95")
        .destAddressRef("1001")
        .destAddressLat(1.3579843)
        .destAddressLng(103.7890947)
        .destZoneId("95")
        .intermediateAddrRef("1001")
        .intermediateAddrLat(1.3579843)
        .intermediateAddrLng(103.7890947)
        .intermediateZoneId("95")
        .vehTypeId(vehTypeId)
        .requestDate(reqDate)
        .build();
  }

  private PlatformFeeResponse getPlatformFeeResponse(String applicableType) {
    return PlatformFeeResponse.builder()
        .id(1L)
        .effectiveFrom("2020-01-01T09:45:00")
        .effectiveTo("2030-01-01T09:45:00")
        .platformFeeThresholdLimit(25.0)
        .lowerPlatformFee(0.5)
        .upperPlatformFee(0.7)
        .deleted(false)
        .platformFeeApplicability(applicableType)
        .build();
  }

  private GenerateRouteResponse getGenerateRouteResponse() {
    return GenerateRouteResponse.builder()
        .duration("1200")
        .distanceMeters(14000L)
        .encodedPolyline(CommonConstant.MOCK_ENCODE_POLYLINE)
        .build();
  }

  @Test
  void givenNullDataInCache_whenStoreFareBreakdownDetail_thenReturnNotFoundException() {
    when(dynpDomainMapper.mapToStoreFareBreakdownRequestEntity(Mockito.any()))
        .thenReturn(getStoreFareBreakdownRequestEntity());
    when(cacheService.getValue(Mockito.any(), Mockito.any())).thenReturn(null);
    StoreFareBreakdownCommandRequest request = getStoreFareBreakdownQueryRequest();
    assertThrows(
        NotFoundException.class, () -> dynamicPricingService.storeFareBreakdownDetail(request));
  }

  @Test
  void givenDataInCache_whenStoreFareBreakdownDetail_thenReturnInternalServerException() {
    when(dynpDomainMapper.mapToStoreFareBreakdownRequestEntity(Mockito.any()))
        .thenReturn(getStoreFareBreakdownRequestEntity());
    when(cacheService.getValue(Mockito.any(), Mockito.any()))
        .thenReturn(FareBreakdownDetailEntity.builder().build());
    doThrow(
            new InternalServerException(
                GET_FARE_BREAK_DOWN_ERROR.getMessage(), GET_FARE_BREAK_DOWN_ERROR.getErrorCode()))
        .when(flatFareBreakDownRepository)
        .createFareBreakdown(Mockito.any());
    StoreFareBreakdownCommandRequest request = getStoreFareBreakdownQueryRequest();
    assertThrows(
        InternalServerException.class,
        () -> dynamicPricingService.storeFareBreakdownDetail(request));
  }

  @Test
  void givenFareIdAlreadyExisted_whenStoreFareBreakdownDetail_thenReturnBadRequestException() {
    when(dynpDomainMapper.mapToStoreFareBreakdownRequestEntity(Mockito.any()))
        .thenReturn(getStoreFareBreakdownRequestEntity());
    when(cacheService.getValue(Mockito.any(), Mockito.any()))
        .thenReturn(FareBreakdownDetailEntity.builder().build());
    when(flatFareBreakDownRepository.isExisted(Mockito.any())).thenReturn(Boolean.TRUE);
    StoreFareBreakdownCommandRequest request = getStoreFareBreakdownQueryRequest();
    assertThrows(
        BadRequestException.class, () -> dynamicPricingService.storeFareBreakdownDetail(request));
  }

  @Test
  void givenDataInCache_whenStoreFareBreakdownDetail_thenReturnSuccess() {
    StoreFareBreakdownCommandRequest request = getStoreFareBreakdownQueryRequest();

    when(dynpDomainMapper.mapToStoreFareBreakdownRequestEntity(Mockito.any()))
        .thenReturn(getStoreFareBreakdownRequestEntity());
    when(cacheService.getValue(
            CommonUtils.generateFareBreakdownKey(request.getFareId(), request.getVehicleTypeId()),
            FareBreakdownDetailEntity.class))
        .thenReturn(FareBreakdownDetailEntity.builder().build());
    when(cacheService.getValue(
            CommonUtils.generateMultiFareCacheKey(request.getFareId()), MultiFareResponse.class))
        .thenReturn(MultiFareResponse.builder().build());
    doNothing().when(flatFareBreakDownRepository).createFareBreakdown(Mockito.any());
    when(mlCreateBookingRequestAggStatsRepository.save(Mockito.any()))
        .thenReturn(new MlCreateBookingRequestAggStatsEntity());
    StoreFareBreakdownCommandResponse response =
        dynamicPricingService.storeFareBreakdownDetail(request);
    assertEquals(Boolean.TRUE, response.getSuccess());
  }

  @Test
  void givenNullRequest_whenSearchFareBreakdown_thenThrowBadRequest() {
    assertThrows(BadRequestException.class, () -> dynamicPricingService.searchFareBreakdown(null));
  }

  @Test
  void givenNullId_whenSearchFareBreakdown_thenThrowBadRequest() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    assertThrows(
        BadRequestException.class, () -> dynamicPricingService.searchFareBreakdown(request));
  }

  @Test
  void givenValidRequest_whenSearchFareBreakdown_thenThrowNotFound() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");
    when(flatFareBreakDownRepository.searchFareBreakdown(
            Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(null);
    assertNull(dynamicPricingService.searchFareBreakdown(request));
  }

  @Test
  void givenValidRequest_whenSearchFareBreakdown_thenReturnResult() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");
    when(flatFareBreakDownRepository.searchFareBreakdown(
            Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(new SearchFareBreakdownResponse());
    assertDoesNotThrow(() -> dynamicPricingService.searchFareBreakdown(request));
  }

  @Test
  void givenValidRequestDataUpstream_whenSearchFareBreakdown_thenReturnResult() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");

    SearchFareBreakdownResponse response = new SearchFareBreakdownResponse();
    response.setUpdatedBy("data_migration");
    response.setDpFinalFare(1.23456);
    response.setMeteredBaseFare(10.0);
    when(flatFareBreakDownRepository.searchFareBreakdown(
            Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(response);

    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setEstimateRateConfig(
        EstimateRateConfig.builder().totalFareEstimateRT("1.5").totalFareEstimateLF("1").build());
    when(cacheService.getValue(anyString(), any())).thenReturn(flatFareConfigSet);

    SearchFareBreakdownResponse actual = dynamicPricingService.searchFareBreakdown(request);

    assertEquals(CommonUtils.roundToTwoBD(1.23456), actual.getTotalFare());
    assertEquals(10.0, actual.getEstimatedFareLF().doubleValue());
    assertEquals(15.0, actual.getEstimatedFareRT().doubleValue());
  }

  @Test
  void givenInvalidConfig_whenSearchFareBreakdown_thenThrowError() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");

    SearchFareBreakdownResponse response = new SearchFareBreakdownResponse();
    response.setUpdatedBy("data_migration");
    when(flatFareBreakDownRepository.searchFareBreakdown(
            Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(response);

    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setEstimateRateConfig(
        EstimateRateConfig.builder()
            .totalFareEstimateRT("string")
            .totalFareEstimateLF("string")
            .build());
    when(cacheService.getValue(anyString(), any())).thenReturn(flatFareConfigSet);

    assertThrows(
        InternalServerException.class, () -> dynamicPricingService.searchFareBreakdown(request));
  }

  @Test
  void givenFareId_whenGetMultiFareByFareId_thenReturnMultiFareResponse() {
    // Given
    String fareId = "sampleFareId";
    MultiFareResponse mockedMultiFareResponse = new MultiFareResponse();

    // When
    when(cacheService.getValue(anyString(), eq(MultiFareResponse.class)))
        .thenReturn(mockedMultiFareResponse);
    MultiFareResponse response = dynamicPricingService.getMultiFareByFareId(fareId);

    // Then
    assertEquals(mockedMultiFareResponse, response);
  }

  @Test
  void givenNonExistentFareId_whenGetMultiFareByFareId_thenThrowsNotFoundException() {
    // Given
    String fareId = "nonExistentFareId";

    // When
    when(cacheService.getValue(anyString(), eq(MultiFareResponse.class))).thenReturn(null);

    // Then
    assertThrows(NotFoundException.class, () -> dynamicPricingService.getMultiFareByFareId(fareId));
  }

  @Test
  void givenValidRequestDataUpstream_whenSearchFareBreakdown_thenReturnResult_withDriverFee() {
    final SearchFareBreakdownRequestEntity request = new SearchFareBreakdownRequestEntity();
    request.setFareId("fareId");
    request.setBookingId("bookingId");
    request.setTripId("tripId");

    SearchFareBreakdownResponse response = new SearchFareBreakdownResponse();
    response.setUpdatedBy("data_migration");
    response.setDpFinalFare(1.23456);
    response.setMeteredBaseFare(10.0);
    when(flatFareBreakDownRepository.searchFareBreakdown(
            Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(response);

    FlatFareConfigSet flatFareConfigSet = new FlatFareConfigSet();
    flatFareConfigSet.setEstimateRateConfig(
        EstimateRateConfig.builder().totalFareEstimateRT("1.5").totalFareEstimateLF("1").build());
    when(cacheService.getValue(anyString(), any())).thenReturn(flatFareConfigSet);

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        AdditionalChargeFeeConfigHelper.generateDriverFeeConfig();

    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(additionalChargeFeeConfigMap);

    SearchFareBreakdownResponse actual = dynamicPricingService.searchFareBreakdown(request);

    assertEquals(CommonUtils.roundToTwoBD(1.23456), actual.getTotalFare());
    assertEquals(10.0, actual.getEstimatedFareLF().doubleValue());
    assertEquals(15.0, actual.getEstimatedFareRT().doubleValue());

    List<AdditionalChargeConfigItem> actualAdditionalCharges = actual.getAdditionalCharges();

    assertNotNull(actualAdditionalCharges);

    AdditionalChargeConfigItem actualAdditionalChargeConfigItem = actualAdditionalCharges.get(0);

    assertNotNull(actualAdditionalChargeConfigItem);

    List<AdditionalChargeFeeConfigResponse> expectedAdditionalChargeFeeConfigList =
        additionalChargeFeeConfigMap.get(AdditionalChargeFeeConfigHelper.DRIVER_FEE);
    AdditionalChargeFeeConfigResponse expectChargeThresholdConfig =
        expectedAdditionalChargeFeeConfigList.get(0);

    // valid chargeId,chargeType,chargeThreshold
    assertEquals(
        expectChargeThresholdConfig.getChargeId(), actualAdditionalChargeConfigItem.getChargeId());
    assertEquals(
        expectChargeThresholdConfig.getChargeType(),
        actualAdditionalChargeConfigItem.getChargeType());

    assertEquals(
        expectChargeThresholdConfig.getChargeValue(),
        actualAdditionalChargeConfigItem.getChargeThreshold(),
        0.01);

    // valid chargeLowerValue
    AdditionalChargeFeeConfigResponse expectChargeLowerValueConfig =
        expectedAdditionalChargeFeeConfigList.get(1);
    assertEquals(
        expectChargeLowerValueConfig.getChargeValue(),
        actualAdditionalChargeConfigItem.getChargeLowerLimit(),
        0.01);

    // valid chargeUpperValue
    AdditionalChargeFeeConfigResponse expectChargeUpperValueConfig =
        expectedAdditionalChargeFeeConfigList.get(2);
    assertEquals(
        expectChargeUpperValueConfig.getChargeValue(),
        actualAdditionalChargeConfigItem.getChargeUpperLimit(),
        0.01);
  }

  @Test
  void getEstimatedFare_normalTypeShowMeterFareOnly_withDriverFee_success() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        AdditionalChargeFeeConfigHelper.generateDriverFeeConfig();

    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(additionalChargeFeeConfigMap);

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isShowMeterFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isShowFlatFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    meterFareForStandard.setMeteredBaseFare(25.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);

    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(meterFareForStandard);

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    FlatFareVOPart actualflatFareVOPart = actual.getFlatFareVOParts().get(0);
    assertNotNull(actualflatFareVOPart);

    List<AdditionalChargeConfigData> actualAdditionalCharges =
        actualflatFareVOPart.getAdditionalCharges();
    assertNotNull(actualAdditionalCharges);

    AdditionalChargeConfigData actualAdditionalChargeConfig = actualAdditionalCharges.get(0);
    assertNotNull(actualAdditionalChargeConfig);

    List<AdditionalChargeFeeConfigResponse> expectedAdditionalChargeFeeConfigList =
        additionalChargeFeeConfigMap.get(AdditionalChargeFeeConfigHelper.DRIVER_FEE);
    AdditionalChargeFeeConfigResponse expectChargeThresholdConfig =
        expectedAdditionalChargeFeeConfigList.get(0);

    // valid chargeId,chargeType,chargeThreshold
    assertEquals(
        expectChargeThresholdConfig.getChargeId(), actualAdditionalChargeConfig.getChargeId());
    assertEquals(
        expectChargeThresholdConfig.getChargeType(), actualAdditionalChargeConfig.getChargeType());
    assertEquals(
        expectChargeThresholdConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeThreshold(),
        0.01);

    // valid chargeLowerValue
    AdditionalChargeFeeConfigResponse expectChargeLowerValueConfig =
        expectedAdditionalChargeFeeConfigList.get(1);
    assertEquals(
        expectChargeLowerValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeLowerLimit(),
        0.01);

    // valid chargeUpperValue
    AdditionalChargeFeeConfigResponse expectChargeUpperValueConfig =
        expectedAdditionalChargeFeeConfigList.get(2);
    assertEquals(
        expectChargeUpperValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeUpperLimit(),
        0.01);

    assertEquals(
        meterFareForStandard.getTotalFare().doubleValue(),
        actual.getFlatFareVOParts().get(0).getTotalFare().doubleValue(),
        0.01);

    assertEquals(
        meterFareForStandard.getEstimatedFareLF().doubleValue(),
        actual.getFlatFareVOParts().get(0).getEstimatedFareLF().doubleValue(),
        0.01);

    assertEquals(
        meterFareForStandard.getEstimatedFareRT().doubleValue(),
        actual.getFlatFareVOParts().get(0).getEstimatedFareRT().doubleValue(),
        0.01);
  }

  @Test
  void getEstimatedFare_normalTypeShowMeterFareOnly_totalFare16_withDriverFee_success()
      throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        AdditionalChargeFeeConfigHelper.generateDriverFeeConfig();

    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(additionalChargeFeeConfigMap);

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isShowMeterFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isShowFlatFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(16.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(15.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(17.0));
    meterFareForStandard.setMeteredBaseFare(16.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);

    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(meterFareForStandard);

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    FlatFareVOPart actualflatFareVOPart = actual.getFlatFareVOParts().get(0);
    assertNotNull(actualflatFareVOPart);

    List<AdditionalChargeConfigData> actualAdditionalCharges =
        actualflatFareVOPart.getAdditionalCharges();
    assertNotNull(actualAdditionalCharges);

    AdditionalChargeConfigData actualAdditionalChargeConfig = actualAdditionalCharges.get(0);
    assertNotNull(actualAdditionalChargeConfig);

    List<AdditionalChargeFeeConfigResponse> expectedAdditionalChargeFeeConfigList =
        additionalChargeFeeConfigMap.get(AdditionalChargeFeeConfigHelper.DRIVER_FEE);
    AdditionalChargeFeeConfigResponse expectChargeThresholdConfig =
        expectedAdditionalChargeFeeConfigList.get(0);

    // valid chargeId,chargeType,chargeThreshold
    assertEquals(
        expectChargeThresholdConfig.getChargeId(), actualAdditionalChargeConfig.getChargeId());
    assertEquals(
        expectChargeThresholdConfig.getChargeType(), actualAdditionalChargeConfig.getChargeType());
    assertEquals(
        expectChargeThresholdConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeThreshold(),
        0.01);

    // valid chargeLowerValue
    AdditionalChargeFeeConfigResponse expectChargeLowerValueConfig =
        expectedAdditionalChargeFeeConfigList.get(1);
    assertEquals(
        expectChargeLowerValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeLowerLimit(),
        0.01);

    // valid chargeUpperValue
    AdditionalChargeFeeConfigResponse expectChargeUpperValueConfig =
        expectedAdditionalChargeFeeConfigList.get(2);
    assertEquals(
        expectChargeUpperValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeUpperLimit(),
        0.01);

    assertEquals(
        meterFareForStandard.getTotalFare().doubleValue(),
        actual.getFlatFareVOParts().get(0).getTotalFare().doubleValue(),
        0.01);

    assertEquals(
        meterFareForStandard.getEstimatedFareLF().doubleValue(),
        actual.getFlatFareVOParts().get(0).getEstimatedFareLF().doubleValue(),
        0.01);

    assertEquals(
        meterFareForStandard.getEstimatedFareRT().doubleValue(),
        actual.getFlatFareVOParts().get(0).getEstimatedFareRT().doubleValue(),
        0.01);
  }

  @Test
  void getEstimatedFare_normalTypeShowFlatFareOnlyAndWaive_withDriverFee_success()
      throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        AdditionalChargeFeeConfigHelper.generateDriverFeeConfig();
    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(additionalChargeFeeConfigMap);

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    doAnswer(
            invocation -> {
              Object[] args = invocation.getArguments();
              ((FlatFareVO) args[0]).setCalculated(Boolean.TRUE);
              ((FlatFareVO) args[0]).setTotalFare(BigDecimal.valueOf(13.0));
              ((FlatFareVO) args[0]).setEstimatedFareLF(BigDecimal.valueOf(12.0));
              ((FlatFareVO) args[0]).setEstimatedFareRT(BigDecimal.valueOf(14.0));
              ((FlatFareVO) args[0]).setFlatFareRequest(getFlatFareRequest(1, reqDate));
              return null;
            })
        .when(dynamicPricingManager)
        .computeTotalFare(Mockito.any(), Mockito.any());

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    FlatFareVOPart actualflatFareVOPart = actual.getFlatFareVOParts().get(0);
    assertNotNull(actualflatFareVOPart);

    List<AdditionalChargeConfigData> actualAdditionalCharges =
        actualflatFareVOPart.getAdditionalCharges();
    assertNotNull(actualAdditionalCharges);

    AdditionalChargeConfigData actualAdditionalChargeConfig = actualAdditionalCharges.get(0);
    assertNotNull(actualAdditionalChargeConfig);

    List<AdditionalChargeFeeConfigResponse> expectedAdditionalChargeFeeConfigList =
        additionalChargeFeeConfigMap.get(AdditionalChargeFeeConfigHelper.DRIVER_FEE);
    AdditionalChargeFeeConfigResponse expectChargeThresholdConfig =
        expectedAdditionalChargeFeeConfigList.get(0);

    // valid chargeId,chargeType,chargeThreshold
    assertEquals(
        expectChargeThresholdConfig.getChargeId(), actualAdditionalChargeConfig.getChargeId());
    assertEquals(
        expectChargeThresholdConfig.getChargeType(), actualAdditionalChargeConfig.getChargeType());
    assertEquals(
        expectChargeThresholdConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeThreshold(),
        0.01);

    // valid chargeLowerValue
    AdditionalChargeFeeConfigResponse expectChargeLowerValueConfig =
        expectedAdditionalChargeFeeConfigList.get(1);
    assertEquals(
        expectChargeLowerValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeLowerLimit(),
        0.01);

    // valid chargeUpperValue
    AdditionalChargeFeeConfigResponse expectChargeUpperValueConfig =
        expectedAdditionalChargeFeeConfigList.get(2);
    assertEquals(
        expectChargeUpperValueConfig.getChargeValue(),
        actualAdditionalChargeConfig.getChargeUpperLimit(),
        0.01);

    assertEquals(
        0, BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getTotalFare()));
    assertEquals(
        0,
        BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getEstimatedFareLF()));
    assertEquals(
        0,
        BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getEstimatedFareRT()));
  }

  @Test
  void
      getEstimatedFare_normalTypeShowFlatFareOnlyAndWaive_WithoutDriverFeeConfigValue_NoFareUpdate()
          throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        AdditionalChargeFeeConfigHelper.generateDriverFeeConfigWithEmptyValue();
    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(additionalChargeFeeConfigMap);

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(anyString(), any()))
        .thenReturn(commonConfigSet)
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(getFlatFareConfigSet())
        .thenReturn(new DynamicPricingConfigSet());

    doAnswer(
            invocation -> {
              Object[] args = invocation.getArguments();
              ((FlatFareVO) args[0]).setCalculated(Boolean.TRUE);
              ((FlatFareVO) args[0]).setTotalFare(BigDecimal.valueOf(13.0));
              ((FlatFareVO) args[0]).setEstimatedFareLF(BigDecimal.valueOf(12.0));
              ((FlatFareVO) args[0]).setEstimatedFareRT(BigDecimal.valueOf(14.0));
              ((FlatFareVO) args[0]).setFlatFareRequest(getFlatFareRequest(1, reqDate));
              return null;
            })
        .when(dynamicPricingManager)
        .computeTotalFare(Mockito.any(), Mockito.any());

    MultiFareResponse actual = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    FlatFareVOPart actualflatFareVOPart = actual.getFlatFareVOParts().get(0);
    assertNotNull(actualflatFareVOPart);

    List<AdditionalChargeConfigData> actualAdditionalCharges =
        actualflatFareVOPart.getAdditionalCharges();
    assertNotNull(actualAdditionalCharges);

    AdditionalChargeConfigData actualAdditionalChargeConfig = actualAdditionalCharges.get(0);
    assertNotNull(actualAdditionalChargeConfig);

    List<AdditionalChargeFeeConfigResponse> expectedAdditionalChargeFeeConfigList =
        additionalChargeFeeConfigMap.get(AdditionalChargeFeeConfigHelper.DRIVER_FEE);
    AdditionalChargeFeeConfigResponse expectChargeThresholdConfig =
        expectedAdditionalChargeFeeConfigList.get(0);

    // valid chargeId,chargeType,chargeThreshold
    assertEquals(
        actualAdditionalChargeConfig.getChargeId(), expectChargeThresholdConfig.getChargeId());
    assertEquals(
        actualAdditionalChargeConfig.getChargeType(), expectChargeThresholdConfig.getChargeType());

    assertNull(actualAdditionalChargeConfig.getChargeThreshold());

    // valid chargeLowerValue
    assertNull(actualAdditionalChargeConfig.getChargeLowerLimit());

    // valid chargeUpperValue
    assertNull(actualAdditionalChargeConfig.getChargeUpperLimit());

    assertEquals(
        0, BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getTotalFare()));
    assertEquals(
        0,
        BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getEstimatedFareLF()));
    assertEquals(
        0,
        BigDecimal.valueOf(13).compareTo(actual.getFlatFareVOParts().get(0).getEstimatedFareRT()));
  }

  @Test
  void getAdditionalChargeFeesByCondition_Success_withUpperDriverFee() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(AdditionalChargeFeeConfigHelper.generateDriverFeeConfig());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isShowMeterFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isShowFlatFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();
    when(cacheService.getValue(CACHE_KEY_COMMON_CONFIG_SET, CommonConfigSet.class))
        .thenReturn(commonConfigSet);
    when(cacheService.getValue(CACHE_KEY_LIMO_CONFIG_SET, FlatFareConfigSet.class))
        .thenReturn(getFlatFareConfigSet());
    when(cacheService.getValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, FlatFareConfigSet.class))
        .thenReturn(getFlatFareConfigSet());
    when(cacheService.getValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, DynamicPricingConfigSet.class))
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(25.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(24.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(26.0));
    meterFareForStandard.setMeteredBaseFare(25.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);

    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(meterFareForStandard);

    MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    when(cacheService.getValue(
            CommonUtils.generateMultiFareCacheKey(multiFareResponse.getFareId()),
            MultiFareResponse.class))
        .thenReturn(multiFareResponse);

    List<AdditionalChargeFeeData> additionalChargeFeesByFareIdAndFareAmount =
        dynamicPricingService.getAdditionalChargeFeesByCondition(
            multiFareResponse.getFareId(),
            multiFareRequestEntity.getVehTypeIDs().get(0),
            FlatfareConstants.STD_PDT_ID);

    assertEquals(
        0.5, additionalChargeFeesByFareIdAndFareAmount.get(0).getChargeAmt().doubleValue(), 0.01);
  }

  @Test
  void getAdditionalChargeFeesByCondition_Success_withLowerDriverFee() throws ParseException {
    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);

    MultiFareRequestQuery multiFareRequestQuery = getEstFareRequestQuery(List.of(1));
    MultiFareRequestEntity multiFareRequestEntity = getEstFareRequestEntity(List.of(1));
    when(dynpDomainMapper.mapToEstFareRequestEntity(Mockito.any()))
        .thenReturn(multiFareRequestEntity);

    when(flatFareManager.createBookingFeeListRequest(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(BookingFeeListRequest.builder().build());
    when(fareService.getBookingFeeByList(Mockito.any()))
        .thenReturn(BookingFeeListResponse.builder().build());

    when(fareService.getAdditionalChargeFeeConfigMap(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(AdditionalChargeFeeConfigHelper.generateDriverFeeConfig());

    GenerateRouteResponse route = getGenerateRouteResponse();
    when(addressService.getRoute(Mockito.any())).thenReturn(route);

    when(flatFareManager.isValidVehTypeId(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isFlatLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstLimoType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isEstFareType(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isDynamicFareType(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);
    when(flatFareManager.isShowMeterFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.TRUE);
    when(flatFareManager.isShowFlatFareOnly(Mockito.anyInt(), any())).thenReturn(Boolean.FALSE);

    CommonConfigSet commonConfigSet =
        CommonConfigSet.builder()
            .cacheTimerMinsBreakdownFlatFare("8")
            .limoFlatFareVehIds("")
            .vehGrpShowFFOnly("")
            .vehGrpShowMeterOnly("")
            .build();

    when(cacheService.getValue(CACHE_KEY_COMMON_CONFIG_SET, CommonConfigSet.class))
        .thenReturn(commonConfigSet);
    when(cacheService.getValue(CACHE_KEY_LIMO_CONFIG_SET, FlatFareConfigSet.class))
        .thenReturn(getFlatFareConfigSet());
    when(cacheService.getValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, FlatFareConfigSet.class))
        .thenReturn(getFlatFareConfigSet());
    when(cacheService.getValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, DynamicPricingConfigSet.class))
        .thenReturn(new DynamicPricingConfigSet());

    FlatFareVO meterFareForStandard = new FlatFareVO();
    meterFareForStandard.setFlatFareRequest(getFlatFareRequest(1, reqDate));
    meterFareForStandard.setTotalFare(BigDecimal.valueOf(15.0));
    meterFareForStandard.setEstimatedFareLF(BigDecimal.valueOf(14.0));
    meterFareForStandard.setEstimatedFareRT(BigDecimal.valueOf(16.0));
    meterFareForStandard.setMeteredBaseFare(15.0);
    meterFareForStandard.setPdtId("FLAT-001");
    meterFareForStandard.setCalculated(Boolean.TRUE);

    when(flatFareManager.computeTotalFare(Mockito.any(), Mockito.any()))
        .thenReturn(meterFareForStandard);

    MultiFareResponse multiFareResponse = dynamicPricingService.getMultiFare(multiFareRequestQuery);

    when(cacheService.getValue(
            CommonUtils.generateMultiFareCacheKey(multiFareResponse.getFareId()),
            MultiFareResponse.class))
        .thenReturn(multiFareResponse);

    List<AdditionalChargeFeeData> additionalChargeFeesByFareIdAndFareAmount =
        dynamicPricingService.getAdditionalChargeFeesByCondition(
            multiFareResponse.getFareId(),
            multiFareRequestEntity.getVehTypeIDs().get(0),
            FlatfareConstants.STD_PDT_ID);

    assertEquals(
        0.3, additionalChargeFeesByFareIdAndFareAmount.get(0).getChargeAmt().doubleValue(), 0.01);
  }

  private StoreFareBreakdownCommandRequest getStoreFareBreakdownQueryRequest() {
    return StoreFareBreakdownCommandRequest.builder().fareId("88331234").vehicleTypeId(100).build();
  }

  private StoreFareBreakdownRequestEntity getStoreFareBreakdownRequestEntity() {
    return StoreFareBreakdownRequestEntity.builder().fareId("88331234").vehicleTypeId(100).build();
  }

  private FlatFareConfigSet getFlatFareConfigSet() throws ParseException {
    Map<String, String> peakHourRate0 = new HashMap<>();
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_DAYS_0", "MON,TUE,WED,THU,FRI,HOL");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_END_TIME_0", "01:30:00");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_RATE_0", "0.25");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_START_TIME_0", "00:30:00");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_10mins_0", "0.15");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_15mins_0", "0.2");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPDOWN_5mins_0", "0.1");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_10mins_0", "0.15");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_15mins_0", "0.1");
    peakHourRate0.put("EST_LIMO_LIVE_TRAFFIC_PEAK_HOUR_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> peakHourRateList = List.of(peakHourRate0);

    Map<String, String> midnightRate0 = new HashMap<>();
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_DAYS_0", "MON,TUE,WED,THU,FRI,HOL");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_END_TIME_0", "01:30:00");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_RATE_0", "0.25");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_START_TIME_0", "00:30:00");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_10mins_0", "0.15");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_15mins_0", "0.2");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPDOWN_5mins_0", "0.1");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_10mins_0", "0.15");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_15mins_0", "0.1");
    midnightRate0.put("EST_LIMO_LIVE_TRAFFIC_MID_NIGHT_STEPUP_5mins_0", "0.2");
    List<Map<String, String>> midnightRateList = List.of(midnightRate0);

    String flagDownRate = "0.5";

    String tierPerCountFare = "400";
    String tierPerCountMeter = "0.25";
    String tierStartDistance = "1000";
    String tierEndDistance = "10000";
    TierFare tierFare =
        TierFare.builder()
            .perCountMeter(tierPerCountFare)
            .perCountFare(tierPerCountMeter)
            .startDistance(tierStartDistance)
            .endDistance(tierEndDistance)
            .build();

    String totalFareEstimateLF = "1";
    String totalFareEstimateRT = "1";
    EstimateRateConfig estimateRateConfig =
        EstimateRateConfig.builder()
            .totalFareEstimateLF(totalFareEstimateLF)
            .totalFareEstimateRT(totalFareEstimateRT)
            .build();

    String durationUnitConfig = "45";
    String durationRateConfig = "0.25";

    String maxFlatFareCap = "200.0";

    String startDateString = "09/20/2023";
    DateFormat df = new SimpleDateFormat("MM/dd/yyyy");
    var reqDate = df.parse(startDateString);
    LocalTime currentTime = DateUtils.convertToLocalTime(reqDate);
    List<LocationSurchargeConfig> locationSurchargeConfigList =
        Stream.concat(
                initLocSurchargeNormalDay(currentTime).stream(),
                initLocSurchargeHOL(currentTime).stream())
            .toList();

    return FlatFareConfigSet.builder()
        .peakHoursRates(peakHourRateList)
        .midnightHoursRates(midnightRateList)
        .flagDownRate(flagDownRate)
        .tier1Fare(tierFare)
        .tier2Fare(tierFare)
        .estimateRateConfig(estimateRateConfig)
        .durationUnitConfig(durationUnitConfig)
        .durationRateConfig(durationRateConfig)
        .maxFlatFareCap(maxFlatFareCap)
        .locationSurchargeConfigList(locationSurchargeConfigList)
        .build();
  }

  private List<LocationSurchargeConfig> initLocSurchargeHOL(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(0.3)
            .applicableDays("HOL")
            .dayIndicator("HOL")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }

  private List<LocationSurchargeConfig> initLocSurchargeNormalDay(LocalTime currentTime) {
    var locItemHOL1 =
        LocationSurchargeConfig.builder()
            .addressRef("origin")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("FLAT-001")
            .chargeBy("PICKUP")
            .surchargeValue(1.9)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL2 =
        LocationSurchargeConfig.builder()
            .addressRef("dest")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();
    var locItemHOL3 =
        LocationSurchargeConfig.builder()
            .addressRef("inter")
            .startTime(currentTime)
            .endTime(currentTime)
            .productId("STD001")
            .chargeBy("DEST")
            .surchargeValue(1.2)
            .applicableDays("WED")
            .dayIndicator("WED")
            .locationName("Changi Airport")
            .build();

    return List.of(locItemHOL1, locItemHOL2, locItemHOL3);
  }
}
