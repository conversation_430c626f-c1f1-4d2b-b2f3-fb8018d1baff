package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationEffectiveCheckEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticBasedConfigurationVersionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.StaticTimeBasedConfigurationEntity;
import java.util.List;

/** Service interface for managing static time-based configurations. */
public interface StaticTimeBasedConfigurationService {

  /**
   * Get all static time-based configuration versions.
   *
   * @return a list of all versions
   */
  List<StaticBasedConfigurationVersionEntity> getStaticTimeBasedConfigurationVersions();

  /**
   * Creates a new static time-based configurations.
   *
   * @param configurations the configurations to create
   * @return the created configurations
   */
  List<StaticTimeBasedConfigurationEntity> batchCreateStaticTimeBasedConfiguration(
      List<StaticTimeBasedConfigurationEntity> configurations);

  /**
   * Gets all static time-based configurations.
   *
   * @param version the version of the configuration to get
   * @return a list of all configurations
   */
  List<StaticTimeBasedConfigurationEntity> getStaticTimeBasedConfigurations(String version);

  /**
   * Gets a static time-based configuration by ID.
   *
   * @param id the ID of the configuration to get
   * @return the configuration, or null if not found
   */
  StaticTimeBasedConfigurationEntity getSurgeComputationTimeBasedStaticConfigurationById(Long id);

  /**
   * Updates an existing static time-based configuration with audit information.
   *
   * @param id the ID of the configuration to update
   * @param configuration the configuration to update
   * @param userId the ID of the user updating the configuration
   * @return the updated configuration, or null if not found
   */
  StaticTimeBasedConfigurationEntity updateSurgeComputationTimeBasedStaticConfiguration(
      Long id, StaticTimeBasedConfigurationEntity configuration, String userId);

  /**
   * Updates an existing static time-based configuration.
   *
   * @param configuration the configuration to update
   * @return the updated configuration, or null if not found
   */
  StaticTimeBasedConfigurationEntity updateSurgeComputationTimeBasedStaticConfiguration(
      StaticTimeBasedConfigurationEntity configuration);

  /**
   * Deletes a static time-based configuration.
   *
   * @param id the ID of the configuration to delete
   * @param userId the ID of the user performing the deletion
   * @return true if the configuration was deleted, false if not found
   */
  boolean deleteSurgeComputationTimeBasedStaticConfiguration(Long id, String userId);

  /**
   * Check if the static time-based configuration out of date or close to expiration
   *
   * @return the effective check entity
   */
  StaticBasedConfigurationEffectiveCheckEntity effectiveCheck();
}
