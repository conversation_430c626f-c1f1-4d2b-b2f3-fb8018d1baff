package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import java.util.List;
import java.util.Optional;

public interface RegionModelDistributionRepository {

  List<RegionModelDistributionEntity> findByRegionId(Long regionId);

  Optional<RegionModelDistributionEntity> findById(Long regionId);

  RegionModelDistributionEntity saveOrUpdate(RegionModelDistributionEntity entity);

  void delete(RegionModelDistributionEntity entity);

  void save(RegionModelDistributionEntity entity);

  long countByModelId(Long modelId);
}
