package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.RegionModelDistributionService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.ConfigurationVersionUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3Region;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.ModelRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.RegionModelDistributionRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.ModelPercentage;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.RegionModelDistributionEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class RegionModelDistributionServiceImpl implements RegionModelDistributionService {

  private final RegionModelDistributionRepository regionModelDistributionRepository;
  private final ModelRepository modelRepository;
  private final AddressService addressService;

  @Override
  public void createOrUpdateRegionModelDistribution(final RegionModelDistributionEntity entity) {
    validTheModelExistOrNot(entity);
    validTheSumOfThePercentages(entity);

    try {
      regionModelDistributionRepository.saveOrUpdate(entity);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("prevent_time_overlap")) {
        log.error(
            "[createOrUpdateRegionModelDistribution] Error create Or update region model distribution with overlapping dates: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR.getErrorCode());
      }

      log.error(
          "[createOrUpdateRegionModelDistribution] Error create Or update region model distribution: ",
          e);
      throw new InternalServerException(
          REGION_MODEL_DISTRIBUTION_CREATE_ERROR.getMessage(),
          REGION_MODEL_DISTRIBUTION_CREATE_ERROR.getErrorCode());
    }
  }

  @Override
  public List<RegionModelDistributionEntity> getRegionModelDistribution(final Long regionId) {
    validTheRegionIdExistOrNot(regionId);

    List<RegionModelDistributionEntity> entities =
        regionModelDistributionRepository.findByRegionId(regionId);

    List<RegionModelDistributionEntity> result;

    List<ModelEntity> allModels = modelRepository.findAll();
    // If empty, will create a default distribution entity with default zero value
    if (entities.isEmpty()) {
      result = buildDefaultDistribution(regionId, allModels);
    } else {
      result = populateModelOrNames(allModels, entities);
    }

    ConfigurationVersionUtils.updateIsInUse(result);
    return result;
  }

  @Override
  public void deleteRegionModelDistribution(final Long id) {
    Optional<RegionModelDistributionEntity> distributionOptional =
        regionModelDistributionRepository.findById(id);

    RegionModelDistributionEntity existingEntity =
        distributionOptional.orElseThrow(
            () -> {
              log.error(
                  "[deleteRegionModelDistribution] Not found region model distribution for id {}",
                  id);
              return new NotFoundException(
                  MessageFormat.format(
                      ErrorEnum.NOT_FOUND_REGION_MODEL_DISTRIBUTION.getMessage(), id),
                  NOT_FOUND_REGION_MODEL_DISTRIBUTION.getErrorCode());
            });

    // Do update to trigger the audit fields update
    regionModelDistributionRepository.save(existingEntity);
    regionModelDistributionRepository.delete(existingEntity);
  }

  @Override
  public Optional<RegionModelDistributionEntity> getEffectiveRegionModelDistribution(
      final Long regionId) {
    List<RegionModelDistributionEntity> entities =
        regionModelDistributionRepository.findByRegionId(regionId);
    if (entities.isEmpty()) {
      log.warn(
          "[getEffectiveRegionModelDistribution] Not found region model distribution for regionId {}",
          regionId);
      return Optional.empty();
    }

    Instant now = Instant.now();
    Optional<RegionModelDistributionEntity> effectiveEntity = findEffectiveData(entities, now);

    if (effectiveEntity.isPresent()) {
      return effectiveEntity;
    }

    return findMostRecentExpiredOne(regionId, entities, now);
  }

  @Override
  public void batchCreateRegionModelDistribution(final List<RegionModelDistributionEntity> entities) {

    List<Long> modelIds = modelRepository.findAllIds();

    for (RegionModelDistributionEntity entity : entities) {
      // 1. Check the sum of the percentages is 100
      validTheSumOfThePercentages(entity);
      // 2. Check the model ids are valid
      validTheModelIdsMatch(entity, modelIds);
    }
    
    // 3. Check the region ids are valid
    validTheRegionIdMatch(entities);

    // 4. Batch save to DB
    try {
      regionModelDistributionRepository.saveAll(entities);
    } catch (Exception e) {
      String errorMessage = e.getMessage();
      if (errorMessage.contains("prevent_time_overlap")) {
        log.error(
            "[batchCreateRegionModelDistribution] Error batch create region model distribution with overlapping dates: ",
            e);
        throw new BadRequestException(
            SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR.getMessage(),
            SURGE_COMPUTATION_MODEL_OVERLAPPING_DATES_ERROR.getErrorCode());
      }

      log.error(
          "[batchCreateRegionModelDistribution] Error batch create region model distribution: ",
          e);
      throw new InternalServerException(
          REGION_MODEL_DISTRIBUTION_CREATE_ERROR.getMessage(),
          REGION_MODEL_DISTRIBUTION_CREATE_ERROR.getErrorCode());
    }
  }

  private static Optional<RegionModelDistributionEntity> findMostRecentExpiredOne(
      final Long regionId, final List<RegionModelDistributionEntity> entities, final Instant now) {
    log.warn(
        "[getEffectiveRegionModelDistribution] No effective distribution found for regionId {}, looking for most recent expired one",
        regionId);

    return entities.stream()
        .filter(
            entity -> {
              boolean hasStarted = now.compareTo(entity.getEffectiveFrom()) >= 0;
              boolean hasExpired =
                  entity.getEffectiveTo() != null && now.compareTo(entity.getEffectiveTo()) >= 0;
              return hasStarted && hasExpired;
            })
        .max(Comparator.comparing(RegionModelDistributionEntity::getEffectiveTo))
        .or(
            () -> {
              log.warn(
                  "[getEffectiveRegionModelDistribution] No effective or expired distribution found for regionId {}",
                  regionId);
              return Optional.empty();
            });
  }

  private static Optional<RegionModelDistributionEntity> findEffectiveData(
      final List<RegionModelDistributionEntity> entities, final Instant now) {
    return entities.stream()
        .filter(
            entity -> {
              boolean afterOrEqualToStart = now.compareTo(entity.getEffectiveFrom()) >= 0;
              boolean beforeEnd =
                  entity.getEffectiveTo() == null || now.compareTo(entity.getEffectiveTo()) < 0;

              return afterOrEqualToStart && beforeEnd;
            })
        .findFirst();
  }

  private void validTheModelExistOrNot(final RegionModelDistributionEntity entity) {
    List<Long> modelIds =
        entity.getModels().stream().map(ModelPercentage::getModelId).collect(Collectors.toList());
    List<Long> existingIds = modelRepository.findIdByIds(modelIds);

    modelIds.removeAll(existingIds);

    if (!modelIds.isEmpty()) {
      log.error(
          "[createOrUpdateRegionModelDistribution] Not found surge computation model for model ids: {}",
          modelIds);
      throw new BadRequestException(
          NOT_FOUND_SURGE_COMPUTATION_MODEL.getMessage(),
          NOT_FOUND_SURGE_COMPUTATION_MODEL.getErrorCode());
    }
  }

  private void validTheSumOfThePercentages(final RegionModelDistributionEntity entity) {
    BigDecimal totalPercentage =
        entity.getModels().stream()
            .map(ModelPercentage::getPercentage)
            .reduce(BigDecimal::add)
            .orElse(BigDecimal.ZERO);
    if (!CommonUtils.isEquals(CommonUtils.ONE_HUNDRED, totalPercentage)) {
      log.warn(
          "[createOrUpdateRegionModelDistribution] The sum of all percentages must be 100, but got {}",
          totalPercentage);
      throw new BadRequestException(
          MessageFormat.format(
              SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR.getMessage(), totalPercentage),
          SURGE_COMPUTATION_MODEL_PERCENTAGE_ERROR.getErrorCode());
    }
  }

  private static List<RegionModelDistributionEntity> populateModelOrNames(
      final List<ModelEntity> allModels, final List<RegionModelDistributionEntity> entities) {
    Map<Long, String> modelMap =
        allModels.stream().collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getModelName));

    Instant now = Instant.now();
    for (final RegionModelDistributionEntity entity : entities) {
      // if is effective, will check if the current distribution include all the models, if not,
      // will fill in the default value
      if (isEffective(entity, now)) {
        entity.setModels(enrichAndMergeModels(entity.getModels(), modelMap));
      } else {
        entity.getModels().forEach(model -> model.setModelName(modelMap.get(model.getModelId())));
      }
    }
    return entities;
  }

  public static List<ModelPercentage> enrichAndMergeModels(
      final List<ModelPercentage> models, final Map<Long, String> modelMap) {
    Map<Long, ModelPercentage> existingModelMap =
        models.stream().collect(Collectors.toMap(ModelPercentage::getModelId, Function.identity()));

    for (Map.Entry<Long, String> entry : modelMap.entrySet()) {
      Long modelId = entry.getKey();
      String modelName = entry.getValue();

      if (existingModelMap.containsKey(modelId)) {
        existingModelMap.get(modelId).setModelName(modelName);
      } else {
        existingModelMap.put(modelId, new ModelPercentage(modelId, BigDecimal.ZERO, modelName));
      }
    }

    return existingModelMap.values().stream()
        .sorted(Comparator.comparing(ModelPercentage::getModelId))
        .collect(Collectors.toList());
  }

  private static boolean isEffective(
      final RegionModelDistributionEntity entity, final Instant now) {
    boolean afterStart = !now.isBefore(entity.getEffectiveFrom()); // now >= effectiveFrom
    boolean beforeEnd =
        (entity.getEffectiveTo() == null)
            || now.isBefore(entity.getEffectiveTo()); // now < effectiveTo or infinity
    return afterStart && beforeEnd;
  }

  private static List<RegionModelDistributionEntity> buildDefaultDistribution(
      final Long regionId, final List<ModelEntity> allModels) {
    List<ModelPercentage> models =
        allModels.stream()
            .map(v -> new ModelPercentage(v.getId(), BigDecimal.ZERO, v.getModelName()))
            .sorted(Comparator.comparing(ModelPercentage::getModelId))
            .toList();

    return List.of(new RegionModelDistributionEntity(regionId, models));
  }

  private void validTheRegionIdExistOrNot(final Long regionId) {
    List<H3Region> regions = addressService.getEffectiveH3Regions();

    boolean regionExists = regions.stream().anyMatch(v -> v.getRegionId().equals(regionId));

    if (!regionExists) {
      log.error("[getRegionModelDistribution] Illegal regionId: {}", regionId);
      throw new BadRequestException(
          MessageFormat.format(REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID.getMessage(), regionId),
          REGION_MODEL_DISTRIBUTION_INVALID_REGION_ID.getErrorCode());
    }
  }

  private void validTheModelIdsMatch(final RegionModelDistributionEntity entity, final List<Long> existingModelIds) {
    List<Long> entityModelIds = entity.getModels().stream()
        .map(ModelPercentage::getModelId)
        .toList();

    // Check if there are any extra model ids in the request that are not in the existing model ids
    List<Long> extraModelIds = entityModelIds.stream()
        .filter(modelId -> !existingModelIds.contains(modelId))
        .collect(Collectors.toList());

    if (!extraModelIds.isEmpty()) {
      log.error(
          "[batchCreateRegionModelDistribution] Invalid model ids, not found in existing models for regionId: {}, model ids: {}",
          entity.getRegionId(), extraModelIds);
      throw new BadRequestException(
          MessageFormat.format(
              REGION_MODEL_DISTRIBUTION_INVALID_MODEL_ID.getMessage(),
              extraModelIds, entity.getRegionId()),
          REGION_MODEL_DISTRIBUTION_INVALID_MODEL_ID.getErrorCode());
    }

    // Check if there are any missing model ids in the request that are in the existing model ids
    List<Long> missingModelIds = existingModelIds.stream()
        .filter(modelId -> !entityModelIds.contains(modelId))
        .collect(Collectors.toList());

    if (!missingModelIds.isEmpty()) {
      log.error(
          "[batchCreateRegionModelDistribution] Missing model ids {} for regionId {}, all model ids must be included",
          missingModelIds, entity.getRegionId());
      throw new BadRequestException(
          MessageFormat.format(
              REGION_MODEL_DISTRIBUTION_MISSING_MODEL_ID.getMessage(),
              missingModelIds, entity.getRegionId()),
          REGION_MODEL_DISTRIBUTION_MISSING_MODEL_ID.getErrorCode());
    }
  }

  private void validTheRegionIdMatch(final List<RegionModelDistributionEntity> entities) {
    List<H3Region> regions = addressService.getEffectiveH3Regions();
    Set<Long> existingRegionIds = regions.stream().map(H3Region::getRegionId).collect(Collectors.toSet());
    Set<Long> entityRegionIds = entities.stream().map(RegionModelDistributionEntity::getRegionId).collect(Collectors.toSet());

    // Check if there are any extra region ids in the request that are not in the existing region ids
    List<Long> extraRegionIds = entityRegionIds.stream()
        .filter(regionId -> !existingRegionIds.contains(regionId))
        .collect(Collectors.toList());

    if (!extraRegionIds.isEmpty()) {
      log.error(
          "[batchCreateRegionModelDistribution] Invalid region ids, not found in existing regions, region ids: {}",
          extraRegionIds);
      throw new BadRequestException(
          MessageFormat.format(
              REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS.getMessage(), extraRegionIds),
          REGION_MODEL_DISTRIBUTION_INVALID_REGION_IDS.getErrorCode());
    }

    // Check if there are any missing region ids in the request that are in the valid region ids
    List<Long> missingRegionIds = existingRegionIds.stream()
        .filter(regionId -> !entityRegionIds.contains(regionId))
        .collect(Collectors.toList());

    if (!missingRegionIds.isEmpty()) {
      log.error(
          "[batchCreateRegionModelDistribution] Missing region ids {}, all regionId ids must be included",
          missingRegionIds);
      throw new BadRequestException(
          MessageFormat.format(
              REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS.getMessage(), missingRegionIds),
          REGION_MODEL_DISTRIBUTION_MISSING_REGION_IDS.getErrorCode());
    }
  }
}
