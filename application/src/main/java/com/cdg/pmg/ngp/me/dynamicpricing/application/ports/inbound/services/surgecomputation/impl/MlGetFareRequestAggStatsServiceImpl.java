package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.MlGetFareRequestAggStatsService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlGetFareRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.FareCountAggregateResult;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@ServiceComponent
@Slf4j
public class MlGetFareRequestAggStatsServiceImpl implements MlGetFareRequestAggStatsService {

  private final RequestCounterService requestCounterService;
  private final MlGetFareRequestAggStatsRepository mlGetFareRequestAggStatsRepository;

  @Override
  public void aggregateGetFareCountEveryMinute(Instant triggerTime) {
    /*
     Because the request count will be stored in local cache first, then sync to Redis every 30 seconds.
      So we need to aggregate the previous minute to get the most up-to-date data. So here minus 2 minutes.
    */
    final Instant endTime =
        triggerTime.truncatedTo(ChronoUnit.MINUTES).minus(1, ChronoUnit.MINUTES);
    final Instant startTime = endTime.minus(1, ChronoUnit.MINUTES);

    aggregateGetFareCountEveryMinute(startTime, endTime);
  }

  public void aggregateGetFareCountEveryMinute(Instant startTime, Instant endTime) {
    List<FareCountAggregateResult> requestCountList =
        requestCounterService.getRequestCount(RequestCountConstant.MULTI_FARE, startTime, endTime);

    if (CollectionUtils.isEmpty(requestCountList)) {
      log.warn(
          "No request count found, endpoint: {}, startTime: {}, endTime: {}",
          RequestCountConstant.MULTI_FARE,
          startTime,
          endTime);
      return;
    }

    final Instant createTime = Instant.now();
    final List<MlGetFareRequestAggStatsEntity> entities =
        requestCountList.stream()
            .map(
                v ->
                    MlGetFareRequestAggStatsEntity.builder()
                        .startTimestamp(startTime)
                        .endTimestamp(endTime)
                        .areaType(v.getAreaType().getValue())
                        .pickupRegionId(v.getH3RegionId())
                        .regionVersion(v.getRegionVersion())
                        .modelId(v.getModelId())
                        .getFareCount(v.getGetFareCount())
                        .createTimestamp(createTime)
                        .build())
            .toList();
    mlGetFareRequestAggStatsRepository.saveAll(entities);
  }
}
