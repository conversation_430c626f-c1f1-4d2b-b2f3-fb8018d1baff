package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants.ROUTE;
import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.*;
import static com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum.*;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.constants.FlatfareConstants;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.RouteInfo;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeConfigData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeDriverFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.additionalcharge.AdditionalChargeFeeData;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.AdditionalChargeDataMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.DynpDomainMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation.MlCreateBookingRequestAggStatsMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.DynamicPricingService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareAdjustmentConfService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.LocationSurchargeService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.additionalcharge.DriverFeeAdditionalChargeProcessor;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.dynamicpricing.impl.DynamicPricingManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.flatfare.impl.FlatFareManager;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.CommonUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.RouteUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.FlatFareBreakDownRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.MlCreateBookingRequestAggStatsRepository;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.FareService;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.commands.StoreFareBreakdownCommandResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.ErrorMessageConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.*;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlCreateBookingRequestAggStatsEntity;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.BookingChannelEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.JobTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.BadRequestException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.DomainException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.NotFoundException;
import com.cdg.pmg.ngp.me.dynamicpricing.queries.MultiFareRequestQuery;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@ServiceComponent
@RequiredArgsConstructor
@Slf4j
public class DynamicPricingServiceImpl implements DynamicPricingService {
  private static final String APPLICABLE_TYPE_YES = "YES";
  private static final String APPLICABLE_TYPE_NO = "NO";
  private static final String APPLICABLE_TYPE_WAIVE = "WAIVE";

  private final FlatFareAdjustmentConfService flatFareAdjustmentConfService;
  private final AddressService addressService;

  private final DynpDomainMapper domainMapper;

  private final FlatFareManager flatFareManager;

  private final DynamicPricingManager dynamicPricingManager;

  private final FareService fareService;

  private final CacheService cacheService;

  private final FlatFareBreakDownRepository flatFareBreakDownRepository;

  private final LocationSurchargeService locationSurchargeService;

  private final DriverFeeAdditionalChargeProcessor driverFeeAdditionalChargeProcessor;

  private final MlCreateBookingRequestAggStatsRepository mlCreateBookingRequestAggStatsRepository;

  private final MlCreateBookingRequestAggStatsMapper mlCreateBookingRequestAggStatsMapper;

  /**
   * Calculates and retrieves a multi-fare response based on the given fare request.
   *
   * <p>This method takes the input query {@link MultiFareRequestQuery}, performs necessary
   * validations, fetches routing and configuration data, and calculates fare estimates for multiple
   * vehicle types using dynamic pricing, flat fares, and additional surcharges. It returns a {@link
   * MultiFareResponse} that contains detailed fare breakdowns for the user-selected vehicle types,
   * along with route and pricing-related metadata.
   *
   * <p>The method performs the following steps:
   *
   * <ul>
   *   <li>Validates the input request:
   *       <ul>
   *         <li>Maps the input {@link MultiFareRequestQuery} to a {@link MultiFareRequestEntity}.
   *         <li>Validates the booking channel and job type using {@link
   *             #validateCalcMultiFareRequest(MultiFareRequestEntity)}.
   *       </ul>
   *   <li>Generates a unique trip ID and fare ID for logging and tracking purposes.
   *   <li>Fetches routing information using the {@link
   *       AddressService#getRoute(GenerateRouteRequest)} method which calculates distance and
   *       estimated travel time.
   *   <li>Validates the routing response to ensure distance, duration, and routing details are
   *       valid. If invalid, a {@link BadRequestException} is thrown with an error message.
   *   <li>Generates a {@link FlatFareRequest}, which is the core data structure used for fare
   *       calculations.
   *   <li>Fetches various configuration details stored in the cache to support fare calculations:
   *       <ul>
   *         <li>Common configuration settings
   *         <li>Flat fare configurations (both standard and advanced types)
   *         <li>Dynamic pricing configuration
   *         <li>Holiday, location surcharge, and additional charge configurations
   *       </ul>
   *   <li>Fetches platform fees, booking fees, and applies surcharge configurations based on the
   *       current request.
   *   <li>Iterates through each vehicle type ID in the request and performs the following:
   *       <ul>
   *         <li>Calculates the estimated fares (flat and/or dynamic) for each vehicle type.
   *         <li>Applies multi-stop surcharges, booking fees, and platform fees as applicable for
   *             each vehicle type.
   *         <li>Optionally calculates driver fees based on job type and booking channel.
   *         <li>Creates {@link FlatFareVOPart} objects for each vehicle type, representing the fare
   *             breakdown.
   *       </ul>
   *   <li>Stores calculated fare data in the cache for potential re-use.
   *   <li>Constructs a {@link MultiFareResponse} containing all the calculated fares and route
   *       details.
   * </ul>
   *
   * @param multiFareRequest the {@link MultiFareRequestQuery} object containing input parameters
   *     such as origin, destination, vehicle types, job type, and booking channel.
   * @return a {@link MultiFareResponse} containing fare details for all requested vehicle types.
   * @throws BadRequestException if:
   *     <ul>
   *       <li>The booking channel or job type is invalid.
   *       <li>Routing information cannot be retrieved or is invalid.
   *       <li>No valid vehicle type IDs are available for fare calculations.
   *     </ul>
   *
   * @throws NotFoundException if configurations or previously cached data needed for fare
   *     calculation are missing.
   * @see MultiFareRequestQuery
   * @see MultiFareResponse
   * @see MultiFareRequestEntity
   * @see AddressService#getRoute(GenerateRouteRequest)
   * @see #validateCalcMultiFareRequest(MultiFareRequestEntity)
   */
  @Override
  public MultiFareResponse getMultiFare(final MultiFareRequestQuery multiFareRequest) {

    MultiFareRequestEntity multiFareRequestEntity =
        domainMapper.mapToEstFareRequestEntity(multiFareRequest);

    validateCalcMultiFareRequest(multiFareRequestEntity);

    MultiFareResponse multiFareResponse = null;

    Date requestTime = Date.from(multiFareRequest.getRequestTime());

    if (multiFareRequestEntity.getFareDate() != null
        && JobTypeEnum.isAdvance(multiFareRequestEntity.getJobType())) {
      requestTime = Date.from(multiFareRequestEntity.getFareDate().toInstant());
    }

    final String tripId = CommonUtils.generateTripId(requestTime);
    final GenerateRouteRequest generateRouteRequest =
        RouteUtils.createGenerateRouteRequest(multiFareRequestEntity, tripId);
    final GenerateRouteResponse route = addressService.getRoute(generateRouteRequest);

    if (RouteUtils.isRouteResponseEmpty(route) || RouteUtils.isRouteResponseEttDistInvalid(route)) {
      log.error("Get route from Address Service empty or invalid");
      throw new BadRequestException(INVALID_ROUTE.getMessage(), INVALID_ROUTE.getErrorCode());
    }

    final RouteInfo routeInfo = RouteUtils.createRouteInfo(multiFareRequestEntity, route, tripId);
    final String fareId =
        CommonUtils.generateFareId(requestTime, multiFareRequestEntity.getMobile());

    final FlatFareRequest flatFareRequest =
        createFlatFareRequest(multiFareRequestEntity, route, requestTime, tripId, fareId);
    log.info("getMultiFare - fareId: {}, flatFareRequest: {} ", fareId, flatFareRequest);

    // GET COMMON CONFIG
    CommonConfigSet commonConfigSet =
        cacheService.getValue(CACHE_KEY_COMMON_CONFIG_SET, CommonConfigSet.class);

    // GET FLAT FARE CONFIG
    FlatFareConfigSet limoConfigSet =
        cacheService.getValue(CACHE_KEY_LIMO_CONFIG_SET, FlatFareConfigSet.class);

    FlatFareConfigSet estStandardConfigSet =
        cacheService.getValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, FlatFareConfigSet.class);

    // GET FARE TYPE CONFIG
    DynamicPricingConfigSet fareTypeConfigSet =
        cacheService.getValue(CACHE_KEY_FARE_TYPE_CONFIG_SET, DynamicPricingConfigSet.class);

    fareTypeConfigSet.setMultiStopSurcharge(limoConfigSet.getMultiStopSurcharge());
    fareTypeConfigSet.setEventSurgeAddressConfigList(
        limoConfigSet.getEventSurgeAddressConfigList());

    // Get Holiday Config
    final List<FlatFareHoliday> holidayList =
        cacheService.getListValue(DYNAMIC_PRICING + COLON + COMPANY_HOLIDAY, FlatFareHoliday.class);
    limoConfigSet.setHolidayList(holidayList);
    estStandardConfigSet.setHolidayList(holidayList);
    fareTypeConfigSet.setHolidayList(holidayList);

    // GET LOC SURCHARGE CONFIG
    final List<LocationSurchargeConfig> locationSurchargeConfigList =
        locationSurchargeService.getLocationSurchargeConfigList(
            flatFareRequest.getRequestDate(),
            flatFareRequest.getOriginAddressRef(),
            flatFareRequest.getIntermediateAddrRef(),
            flatFareRequest.getDestAddressRef(),
            holidayList);
    limoConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);
    estStandardConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);
    fareTypeConfigSet.setLocationSurchargeConfigList(locationSurchargeConfigList);

    // Get Booking Fee List
    final BookingFeeListRequest bookingFeeListRequest =
        flatFareManager.createBookingFeeListRequest(
            flatFareRequest, commonConfigSet, limoConfigSet.getHolidayList());
    final BookingFeeListResponse bookingFeeListResponse =
        fareService.getBookingFeeByList(bookingFeeListRequest);
    limoConfigSet.setBookingFeeList(bookingFeeListResponse.getBookingFeeList());
    estStandardConfigSet.setBookingFeeList(bookingFeeListResponse.getBookingFeeList());

    // Get Platform Fee Config
    final PlatformFeeListRequest platformFeeListRequest =
        flatFareManager.createPlatformFeeListRequest(flatFareRequest, commonConfigSet);
    final List<PlatformFeeResponse> platformFeeConfigList =
        fareService.getPlatformFeeByList(platformFeeListRequest);

    final List<FlatFareVOPart> flatFareVOPartList = new ArrayList<>();

    final int timeExpireMultiFare =
        flatFareManager.getCacheTimerMultiFlatFareInMinute(
            commonConfigSet.getCacheTimerMinsMultiFlatFare());
    final Date calculatedDate = new Date();

    if (JobTypeEnum.isAdvance(multiFareRequestEntity.getJobType())) {
      final List<Integer> advanceVehIdList =
          flatFareManager.filterAdvanceVehId(
              multiFareRequestEntity.getVehTypeIDs(), commonConfigSet);
      multiFareRequestEntity.setVehTypeIDs(advanceVehIdList);
    }

    // Get additional charge fee config from fare-svc
    // "additionalChargeFeeConfigMap" for this map, key=chargeType, value=list of
    // AdditionalChargeConfig for same chargeType
    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        new HashMap<>();
    try {
      additionalChargeFeeConfigMap = fareService.getAdditionalChargeFeeConfigMap(null, null, null);
    } catch (Exception e) {
      log.error("Get additional charge fee config from fare-svc failed.", e);
    }

    List<FlatFareAdjustmentConfEntity> flatFareAdjustmentConfEntities =
        flatFareAdjustmentConfService.getFlatFareAdjustmentConf();

    for (final int vehTypeId : multiFareRequestEntity.getVehTypeIDs()) {
      flatFareRequest.setVehTypeId(vehTypeId);
      EstimatedFare estimatedFare;
      try {
        estimatedFare =
            computeEstimatedFare(
                flatFareRequest,
                limoConfigSet,
                estStandardConfigSet,
                commonConfigSet,
                fareTypeConfigSet);
      } catch (Exception exception) {
        log.error(
            "Unable To calculate vehTypeId={} exception={}", vehTypeId, exception.getMessage());
        continue;
      }

      final FlatFareVO finalFlatFare = combineEstimatedFlatFare(estimatedFare);

      // Driver Fee requirement ,jira ticket:
      // https://comfortdelgrotaxi.atlassian.net/browse/NGPME-9207
      boolean isNeedCalculateDriverFee = isNeedCalculateDriverFee(multiFareRequestEntity);
      final boolean isDynamicPrice = estimatedFare.getDynamicPricingForStandard().isCalculated();

      final int surgeLvl =
          flatFareManager.computeSurgeLevel(
              isDynamicPrice,
              estimatedFare.getDynamicPricingForStandard(),
              commonConfigSet.getDriverSurgeLevelIndications());
      final int surgeIndicator =
          flatFareManager.computeSurgeIndicator(
              isDynamicPrice,
              estimatedFare.getDynamicPricingForStandard(),
              commonConfigSet.getSurgeIndicatorThreshold(),
              commonConfigSet.getSurgeIndicatorThresholdZero());

      FlatFareVOPart flatFarePart = null;
      if ((!flatFareManager.isShowMeterFareOnly(vehTypeId, commonConfigSet))
          && (!JobTypeEnum.isAdvance(flatFareRequest.getJobType()))) {
        flatFarePart =
            buildFlatFarePart(
                finalFlatFare,
                surgeLvl,
                surgeIndicator,
                platformFeeConfigList,
                isNeedCalculateDriverFee,
                additionalChargeFeeConfigMap);

        BigDecimal finalFare =
            adjustFare(
                flatFarePart.getTotalFare(),
                finalFlatFare.getFlatFareRequest().getVehTypeId(),
                flatFareAdjustmentConfEntities);
        flatFarePart.setTotalFare(finalFare);
        flatFarePart.setEstimatedFareLF(finalFare);
        flatFarePart.setEstimatedFareRT(finalFare);

        flatFareVOPartList.add(flatFarePart);
      }

      FlatFareVOPart meterFarePart = null;
      if (!flatFareManager.isShowFlatFareOnly(vehTypeId, commonConfigSet)) {
        meterFarePart =
            buildMeterFarePart(
                finalFlatFare,
                platformFeeConfigList,
                isNeedCalculateDriverFee,
                additionalChargeFeeConfigMap);
        flatFareVOPartList.add(meterFarePart);
      }
      storeFareBreakdownToCache(
          finalFlatFare, flatFarePart, meterFarePart, timeExpireMultiFare, multiFareRequestEntity);
    }

    if (!flatFareVOPartList.isEmpty()) {
      multiFareResponse =
          createEstimatedFareResponse(
              flatFareRequest, flatFareVOPartList, calculatedDate, timeExpireMultiFare, fareId);

      storeMultiFareToCache(fareId, multiFareResponse);
      storeRouteInfoToCache(routeInfo, timeExpireMultiFare);
    }

    if (Objects.isNull(multiFareResponse)) {
      throw new BadRequestException(
          NO_VEH_TYPE_ID_AVAILABLE.getMessage(), NO_VEH_TYPE_ID_AVAILABLE.getErrorCode());
    }

    return multiFareResponse;
  }

  /**
   * Below situation will not need to calculate driver fee: 1. jobType is STREET 2. jobType is not
   * STREET,but booking channel is IVR,CSA or SMS
   *
   * @param multiFareRequestEntity use jobType and booking channel to judge
   * @return true - calculate driver fee ; false - not calculate driver fee
   */
  private boolean isNeedCalculateDriverFee(MultiFareRequestEntity multiFareRequestEntity) {
    // If the job type is STREET,then not need to calculate driver fee
    if (JobTypeEnum.isStreet(multiFareRequestEntity.getJobType())) {
      return false;
    } else {
      return BookingChannelEnum.isNeedCalculateDriverFee(
          multiFareRequestEntity.getBookingChannel());
    }
  }

  @Override
  public FareDetail getMultiFareDetail(final MultiFareRequestQuery multiFareRequest) {
    final FareDetail fareDetail = new FareDetail();

    final MultiFareResponse multiFareResponse = getMultiFare(multiFareRequest);

    final String fareBreakdownKey =
        CommonUtils.generateFareBreakdownKey(
            multiFareResponse.getFareId(), multiFareRequest.getVehTypeIDs().get(0));
    log.info("fareBreakdownKey={}", fareBreakdownKey);
    final FareBreakdownDetailEntity fareBreakdownDetail =
        cacheService.getValue(fareBreakdownKey, FareBreakdownDetailEntity.class);

    fareDetail.setMultiFareResponse(multiFareResponse);
    fareDetail.setFareBreakdown(fareBreakdownDetail);

    return fareDetail;
  }

  @Override
  public MultiFareResponse getMultiFareByFareId(final String fareId) {
    final MultiFareResponse fareInCache = getMultiFareResponseFromCache(fareId);
    if (Objects.isNull(fareInCache)) {
      log.error("Error when get multi fare by fare id: {}", fareId);
      throw new NotFoundException(
          MULTI_FARE_NOT_FOUND.getMessage(), MULTI_FARE_NOT_FOUND.getErrorCode());
    }
    return fareInCache;
  }

  @Override
  public boolean validateFare(ValidateFareEntity validateFareRequest) {
    final MultiFareResponse fareInCache =
        getMultiFareResponseFromCache(validateFareRequest.getFareId());
    if (Objects.nonNull(fareInCache)) {
      return fareInCache.isValidFare(validateFareRequest);
    }
    return Boolean.FALSE;
  }

  @Override
  public GeneratedRouteEntity getGeneratedRouteByTripId(final String tripId) {
    if (tripId.isBlank()) {
      log.error("Trip id is blank");
      throw new BadRequestException(TRIP_ID_BLANK.getMessage(), TRIP_ID_BLANK.getErrorCode());
    }
    String cacheKey = DYNAMIC_PRICING + COLON + ROUTE + COLON + tripId;
    com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo routeInfo =
        cacheService.getValue(cacheKey, com.cdg.pmg.ngp.me.dynamicpricing.entities.RouteInfo.class);
    if (Objects.isNull(routeInfo)) {
      log.info("Get generated route info from database");
      return domainMapper.mapRouteInfoToGeneratedRouteEntity(
          flatFareBreakDownRepository.getGeneratedRouteByTripId(tripId));
    }
    log.info("Get generated route info from cache");
    return domainMapper.mapRouteInfoToGeneratedRouteEntity(routeInfo);
  }

  @Override
  public SearchFareBreakdownResponse searchFareBreakdown(
      final SearchFareBreakdownRequestEntity request) {
    log.info("Start search fare breakdown");
    if (Objects.isNull(request)
        || ObjectUtils.allNull(request.getBookingId(), request.getFareId(), request.getTripId())) {
      log.error("Invalid search fare breakdown request");
      throw new BadRequestException(
          SEARCH_FARE_BREAKDOWN_REQUEST_INVALID.getMessage(),
          SEARCH_FARE_BREAKDOWN_REQUEST_INVALID.getErrorCode());
    }

    final SearchFareBreakdownResponse response =
        flatFareBreakDownRepository.searchFareBreakdown(
            request.getFareId(), request.getTripId(), request.getBookingId());

    // updateBy is NOT empty mean this data was upstream from CN3
    if (Objects.nonNull(response) && StringUtils.isNotEmpty(response.getUpdatedBy())) {
      handleFareBreakdownUpstreamData(response);
    }

    // DRIVER_FEE requirement, UCD :
    // https://comfortdelgrotaxi.atlassian.net/wiki/spaces/NGP/pages/1462600275/4.13.9+Release2+CPF+Driver+Fee+Driver+Benefit+Technical+Design

    // Get additional charge fee config from fare-svc
    // "additionalChargeFeeConfigMap" for this map, key=chargeType, value=list of
    // AdditionalChargeConfig for same chargeType
    Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap =
        new HashMap<>();
    try {
      additionalChargeFeeConfigMap = fareService.getAdditionalChargeFeeConfigMap(null, null, null);
    } catch (Exception e) {
      log.error("Get additional charge fee config from fare-svc failed.", e);
    }

    // Add additional charges(config data) to fare break down data
    List<AdditionalChargeConfigItem> additionalCharges = new ArrayList<>();
    driverFeeAdditionalChargeProcessor
        .calculateAdditionalCharge(
            generateAdditionalChargeParamFromSearchFareBreakdownResponse(response),
            additionalChargeFeeConfigMap)
        .ifPresent(
            additionalChargeDriverFeeData ->
                additionalCharges.add(
                    AdditionalChargeDataMapper.convertToAdditionalChargeConfigItem(
                        additionalChargeDriverFeeData)));
    if (!additionalCharges.isEmpty()) {
      response.setAdditionalCharges(additionalCharges);
    }

    log.info(
        MessageFormat.format(
            ErrorMessageConstant.SEARCH_FARE_BREAKDOWN_FOUND,
            request.getBookingId(),
            request.getFareId(),
            request.getTripId(),
            response));

    return response;
  }

  /**
   * Generate para for calculateAdditionalCharge from SearchFareBreakdownResponse
   *
   * @param response SearchFareBreakdownResponse
   * @return FlatFareVOPart for calculateAdditionalCharge use as param
   */
  private FlatFareVOPart generateAdditionalChargeParamFromSearchFareBreakdownResponse(
      SearchFareBreakdownResponse response) {

    if (null == response) {
      return null;
    }

    FlatFareVOPart flatFareVOPart = new FlatFareVOPart();
    flatFareVOPart.setTotalFare(response.getTotalFare());
    flatFareVOPart.setEstimatedFareRT(response.getEstimatedFareRT());
    flatFareVOPart.setEstimatedFareLF(response.getEstimatedFareLF());

    return flatFareVOPart;
  }

  @Override
  public List<AdditionalChargeFeeData> getAdditionalChargeFeesByCondition(
      String fareId, Integer vehTypeId, String productTypeId) {
    log.info(
        "getAdditionalChargeFeesByCondition " + "fareId={}, vehTypeId={}, productTypeId={} ",
        fareId,
        vehTypeId,
        productTypeId);
    if (StringUtils.isEmpty(fareId) || null == vehTypeId || StringUtils.isEmpty(productTypeId)) {
      return List.of();
    }

    // 1. Generate estimatedFareKeyCache from [fareId]
    final String estimatedFareKeyCache = CommonUtils.generateMultiFareCacheKey(fareId);

    /*
    2. Get MultiFareResponse from cache. This data will set to cache when api
    /v1.0/pricing/multi-fare be called
     */
    final MultiFareResponse fareInCache =
        cacheService.getValue(estimatedFareKeyCache, MultiFareResponse.class);
    log.info(
        "getAdditionalChargeFeesByCondition estimatedFareKeyCache={}, fareInCache={}",
        estimatedFareKeyCache,
        fareInCache);
    // 3. If no MultiFareResponse in cache ,return empty list
    if (fareInCache == null) {
      return List.of();
    }
    // 4. If MultiFareResponse exists
    else {

      // 4.1 Get List<FlatFareVOPart> from MultiFareResponse
      final List<FlatFareVOPart> flatFareVOPartsInCache = fareInCache.getFlatFareVOParts();

      // 4.2 If List<FlatFareVOPart> null or empty,then return empty list
      if (CollectionUtils.isEmpty(flatFareVOPartsInCache)) {
        return List.of();
      } else {
        final List<AdditionalChargeFeeData> additionalChargeFeeDataList = new ArrayList<>();

        // 4.3 Loop List<FlatFareVOPart> to generate AdditionalChargeFeeData
        flatFareVOPartsInCache.forEach(
            flatFareVOPart -> {
              /*
              4.4 If match data use [fareAmount,vehTypeId,productTypeId] ,transfer it to AdditionalChargeFeeData.
              Use compareTo for fareAmount and totalFare because it only needs to compare the value of the numbers,
              ignoring excess precision.
               */
              if (vehTypeId.equals(flatFareVOPart.getVehTypeId())
                  && productTypeId.equalsIgnoreCase(flatFareVOPart.getPdtId())) {
                // Currently only have DRIVER_FEE type additional charge fee
                AdditionalChargeDriverFeeData additionalChargeDriverFeeData =
                    flatFareVOPart.getAdditionalChargeDriverFeeData();

                AdditionalChargeFeeData additionalChargeFeeData = null;

                if (additionalChargeDriverFeeData != null) {
                  additionalChargeFeeData =
                      AdditionalChargeDataMapper.convertToAdditionalChargeFeeData(
                          additionalChargeDriverFeeData);
                }

                if (additionalChargeFeeData != null) {
                  additionalChargeFeeDataList.add(additionalChargeFeeData);
                }
              }
            });
        log.info(
            "getAdditionalChargeFeesByCondition result , fareId={}, additionalChargeFeeDataList={}.",
            fareId,
            additionalChargeFeeDataList);
        return additionalChargeFeeDataList;
      }
    }
  }

  private MultiFareResponse getMultiFareResponseFromCache(final String fareId) {
    final String estimatedFareKeyCache = CommonUtils.generateMultiFareCacheKey(fareId);
    return cacheService.getValue(estimatedFareKeyCache, MultiFareResponse.class);
  }

  private void handleFareBreakdownUpstreamData(final SearchFareBreakdownResponse response) {
    double dpFinalFare = response.getDpFinalFare() == null ? 0.0 : response.getDpFinalFare();
    double baseMeterFare =
        response.getMeteredBaseFare() == null ? 0.0 : response.getMeteredBaseFare();

    try {
      final FlatFareConfigSet estStandardConfigSet =
          cacheService.getValue(CACHE_KEY_EST_STANDARD_CONFIG_SET, FlatFareConfigSet.class);
      final EstimateRateConfig estimateRateConfig = estStandardConfigSet.getEstimateRateConfig();

      double estimatedLFRate = Double.parseDouble(estimateRateConfig.getTotalFareEstimateLF());
      double estimatedRTRate = Double.parseDouble(estimateRateConfig.getTotalFareEstimateRT());

      response.setEstimatedFareLF(CommonUtils.roundToTwoBD(baseMeterFare * estimatedLFRate));
      response.setEstimatedFareRT(CommonUtils.roundToTwoBD(baseMeterFare * estimatedRTRate));
      response.setTotalFare(CommonUtils.roundToTwoBD(dpFinalFare));
    } catch (Exception e) {
      throw new InternalServerException(
          SEARCH_FARE_BREAKDOWN_CONVERT_ERROR.getMessage(),
          SEARCH_FARE_BREAKDOWN_CONVERT_ERROR.getErrorCode());
    }
  }

  /**
   * Validates the input {@link MultiFareRequestEntity} for calculating multi-fare requests.
   *
   * <p>Performs the following validations:
   *
   * <ul>
   *   <li>Checks if the booking channel in the request is a valid enumeration value of {@link
   *       BookingChannelEnum}. Throws a {@link BadRequestException} if invalid.
   *   <li>Checks if the job type in the request is a valid enumeration value of {@link
   *       JobTypeEnum}. Throws a {@link BadRequestException} if invalid.
   * </ul>
   *
   * @param request the {@link MultiFareRequestEntity} containing fare request details.
   * @throws BadRequestException if:
   *     <ul>
   *       <li>The booking channel is invalid.
   *       <li>The job type is invalid.
   *     </ul>
   */
  private void validateCalcMultiFareRequest(final MultiFareRequestEntity request) {
    if (!EnumUtils.isValidEnum(BookingChannelEnum.class, request.getBookingChannel())) {
      throw new BadRequestException(
          INVALID_BOOKING_CHANNEL.getMessage(), INVALID_BOOKING_CHANNEL.getErrorCode());
    }
    if (!EnumUtils.isValidEnum(JobTypeEnum.class, request.getJobType())) {
      throw new BadRequestException(INVALID_JOB_TYPE.getMessage(), INVALID_JOB_TYPE.getErrorCode());
    }
  }

  private EstimatedFare computeEstimatedFare(
      final FlatFareRequest flatFareRequest,
      final FlatFareConfigSet limoConfigSet,
      final FlatFareConfigSet estStandardConfigSet,
      final CommonConfigSet commonConfigSet,
      final DynamicPricingConfigSet fareTypeConfigSet) {
    log.info("Compute Estimated Fare with vehTypeId={}", flatFareRequest.getVehTypeId());

    final int vehTypeIdReq = flatFareRequest.getVehTypeId();

    if (!flatFareManager.isValidVehTypeId(vehTypeIdReq, commonConfigSet)) {
      throw new DomainException(
          INVALID_VEH_TYPE_ID.getMessage(), INVALID_VEH_TYPE_ID.getErrorCode());
    }

    FlatFareVO flatAndMeterFareForLimo = new FlatFareVO();
    FlatFareVO meterFareForStandard = new FlatFareVO();
    FlatFareVO dynamicPricingForStandard = new FlatFareVO();

    if (flatFareManager.isFlatLimoType(vehTypeIdReq, commonConfigSet)
        || flatFareManager.isEstLimoType(vehTypeIdReq, commonConfigSet)) {
      flatAndMeterFareForLimo = flatFareManager.computeTotalFare(limoConfigSet, flatFareRequest);
    } else {
      if (flatFareManager.isEstFareType(vehTypeIdReq, commonConfigSet)) {
        meterFareForStandard =
            flatFareManager.computeTotalFare(estStandardConfigSet, flatFareRequest);
      }
      if (flatFareManager.isDynamicFareType(vehTypeIdReq, commonConfigSet)) {
        dynamicPricingForStandard =
            computeDynamicPricing(
                meterFareForStandard.getMeteredBaseFare(),
                meterFareForStandard.getEstimatedFareLF(),
                meterFareForStandard.getEstimatedFareRT(),
                fareTypeConfigSet,
                flatFareRequest);
      }
    }

    log.info(
        "[Calc Multi Fare] "
            + flatFareRequest
            + ", flatAndMeterFareForLimo="
            + (flatAndMeterFareForLimo.isCalculated() ? flatAndMeterFareForLimo : "null")
            + ", meterFareForStandard="
            + (meterFareForStandard.isCalculated() ? meterFareForStandard : "null")
            + ", dynamicPricingForStandard="
            + (dynamicPricingForStandard.isCalculated() ? dynamicPricingForStandard : "null"));

    return EstimatedFare.builder()
        .flatAndMeterFareForLimo(flatAndMeterFareForLimo)
        .meterFareForStandard(meterFareForStandard)
        .dynamicPricingForStandard(dynamicPricingForStandard)
        .build();
  }

  private FlatFareVO computeDynamicPricing(
      final double meteredBaseFare,
      final BigDecimal estimatedFareLF,
      final BigDecimal estimatedFareRT,
      final DynamicPricingConfigSet fareTypeConfigSet,
      final FlatFareRequest flatFareRequest) {
    final FlatFareVO dynamicPricing = new FlatFareVO();
    dynamicPricing.setFlatFareRequest(flatFareRequest);
    dynamicPricing.setMeteredBaseFare(meteredBaseFare);
    dynamicPricing.setEstimatedFareLF(estimatedFareLF);
    dynamicPricing.setEstimatedFareRT(estimatedFareRT);
    dynamicPricingManager.computeTotalFare(dynamicPricing, fareTypeConfigSet);
    dynamicPricingManager.validateDesurge(dynamicPricing, fareTypeConfigSet);
    dynamicPricingManager.setLimitMinMax(dynamicPricing, fareTypeConfigSet);
    return dynamicPricing;
  }

  private BigDecimal adjustFare(
      BigDecimal totalFare, int vehGrp, List<FlatFareAdjustmentConfEntity> configs) {

    Optional<FlatFareAdjustmentConfEntity> optflatFareAdjustmentConfEntity =
        configs.stream().filter(config -> vehGrp == config.getVehGrp()).findFirst();

    if (optflatFareAdjustmentConfEntity.isPresent()) {
      double fare = totalFare.doubleValue();
      FlatFareAdjustmentConfEntity flatFareAdjustmentConfEntity =
          optflatFareAdjustmentConfEntity.get();
      fare =
          fare
              + flatFareAdjustmentConfEntity.getFixedVal()
              + (fare * flatFareAdjustmentConfEntity.getPerVal() / 100);

      return BigDecimal.valueOf(CommonUtils.roundTo(fare, 1));
    } else return totalFare;
  }

  private FlatFareVOPart buildFlatFarePart(
      final FlatFareVO finalFlatFare,
      final int surgeLvl,
      final int surgeIndicator,
      final List<PlatformFeeResponse> platformFeeConfigList,
      final boolean isNeedCalculateDriverFee,
      final Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap) {
    final FlatFareVOPart flatFarePart =
        FlatFareVOPart.builder()
            .vehTypeId(finalFlatFare.getFlatFareRequest().getVehTypeId())
            .totalFare(finalFlatFare.getTotalFare())
            .estimatedFareLF(finalFlatFare.getTotalFare())
            .estimatedFareRT(finalFlatFare.getTotalFare())
            .pdtId(finalFlatFare.getPdtId())
            .drvSurgeLvl(
                FlatfareConstants.NORMAL_FLATFARE_PDT_ID.equalsIgnoreCase(finalFlatFare.getPdtId())
                    ? surgeLvl
                    : 0)
            .paxSurgeIndicator(
                FlatfareConstants.NORMAL_FLATFARE_PDT_ID.equalsIgnoreCase(finalFlatFare.getPdtId())
                    ? surgeIndicator
                    : 0)
            .build();
    /*
    Driver Fee requirement ,jira ticket - https://comfortdelgrotaxi.atlassian.net/browse/NGPME-9207
    Calculate driver fee ,then add driver to totalFare,estimatedFareLF,estimatedFareRT
     */
    if (isNeedCalculateDriverFee) {
      Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeDataOptional =
          driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
              flatFarePart, additionalChargeFeeConfigMap);

      List<AdditionalChargeConfigData> additionalCharges = new ArrayList<>();

      additionalChargeDriverFeeDataOptional.ifPresent(
          additionalChargeDriverFeeData -> {
            /*
            Set additionalChargeDriverFeeData to the return flatFarePart,
            will store to cache along with flatFarePart for api /v1.0/pricing/additional-charge-fees use
             */
            flatFarePart.setAdditionalChargeDriverFeeData(additionalChargeDriverFeeData);

            // Collect additional charge config data
            additionalCharges.add(
                AdditionalChargeDataMapper.convertToAdditionalChargeConfigData(
                    additionalChargeDriverFeeData));
          });

      // Set additional charge config data list to the return value "flatFarePart"
      flatFarePart.setAdditionalCharges(additionalCharges);
    }

    final PlatformFee flatfarePlatformFee =
        computePlatformFee(flatFarePart, finalFlatFare.getFlatFareRequest(), platformFeeConfigList);
    flatFarePart.setPlatformFeeConfigId(flatfarePlatformFee.getConfigId());
    flatFarePart.setPlatformFeeLower(flatfarePlatformFee.getPlatformFeeLower());
    flatFarePart.setPlatformFeeUpper(flatfarePlatformFee.getPlatformFeeUpper());

    return flatFarePart;
  }

  private FlatFareVOPart buildMeterFarePart(
      final FlatFareVO finalFlatFare,
      final List<PlatformFeeResponse> platformFeeConfigList,
      final boolean isNeedCalculateDriverFee,
      final Map<String, List<AdditionalChargeFeeConfigResponse>> additionalChargeFeeConfigMap) {
    final FlatFareVOPart meterFarePart =
        FlatFareVOPart.builder()
            .vehTypeId(finalFlatFare.getFlatFareRequest().getVehTypeId())
            .totalFare(finalFlatFare.getTotalFare())
            .estimatedFareLF(finalFlatFare.getEstimatedFareLF())
            .estimatedFareRT(finalFlatFare.getEstimatedFareRT())
            .pdtId(FlatfareConstants.STD_PDT_ID)
            .drvSurgeLvl(0)
            .paxSurgeIndicator(0)
            .build();
    /*
    Driver Fee requirement ,jira ticket - https://comfortdelgrotaxi.atlassian.net/browse/NGPME-9207
    Calculate driver fee ,then add driver to totalFare,estimatedFareLF,estimatedFareRT
     */
    if (isNeedCalculateDriverFee) {

      Optional<AdditionalChargeDriverFeeData> additionalChargeDriverFeeDataOptional =
          driverFeeAdditionalChargeProcessor.calculateAdditionalCharge(
              meterFarePart, additionalChargeFeeConfigMap);

      List<AdditionalChargeConfigData> additionalCharges = new ArrayList<>();

      additionalChargeDriverFeeDataOptional.ifPresent(
          additionalChargeDriverFeeData -> {
            /*
            Set additionalChargeDriverFeeData to the return flatFarePart,
            will store to cache along with meterFarePart for api xxx use
             */
            meterFarePart.setAdditionalChargeDriverFeeData(additionalChargeDriverFeeData);

            // Collect additional charge config data
            additionalCharges.add(
                AdditionalChargeDataMapper.convertToAdditionalChargeConfigData(
                    additionalChargeDriverFeeData));
          });

      // Set additional charge config data list to the return value "meterFarePart"
      meterFarePart.setAdditionalCharges(additionalCharges);
    }

    final PlatformFee meterfarePlatformFee =
        computePlatformFee(
            meterFarePart, finalFlatFare.getFlatFareRequest(), platformFeeConfigList);
    meterFarePart.setPlatformFeeConfigId(meterfarePlatformFee.getConfigId());
    meterFarePart.setPlatformFeeLower(meterfarePlatformFee.getPlatformFeeLower());
    meterFarePart.setPlatformFeeUpper(meterfarePlatformFee.getPlatformFeeUpper());
    return meterFarePart;
  }

  private void storeFareBreakdownToCache(
      final FlatFareVO flatFareVO,
      final FlatFareVOPart flatFarePart,
      final FlatFareVOPart meterFarePart,
      final int timeExpireBreakDown,
      final MultiFareRequestEntity requestEntity) {

    final FlatFareRequest flatFareRequest = flatFareVO.getFlatFareRequest();
    final long flatFarePlatformFeeId;
    final double flatFarePlatformFee;
    final long meterPlatformFeeId;
    final double meterPlatformFeeLower;
    final double meterPlatformFeeUpper;

    if (flatFarePart == null) {
      flatFarePlatformFeeId = FlatfareConstants.PLATFORM_FEE_ID_DEFAULT;
      flatFarePlatformFee = FlatfareConstants.PRICE_DEFAULT;
    } else {
      flatFarePlatformFeeId = flatFarePart.getPlatformFeeConfigId();
      flatFarePlatformFee = flatFarePart.getPlatformFeeUpper();
    }

    if (meterFarePart == null) {
      meterPlatformFeeId = FlatfareConstants.PLATFORM_FEE_ID_DEFAULT;
      meterPlatformFeeLower = FlatfareConstants.PRICE_DEFAULT;
      meterPlatformFeeUpper = FlatfareConstants.PRICE_DEFAULT;
    } else {
      meterPlatformFeeId = meterFarePart.getPlatformFeeConfigId();
      meterPlatformFeeLower = meterFarePart.getPlatformFeeLower();
      meterPlatformFeeUpper = meterFarePart.getPlatformFeeUpper();
    }

    var streamFlatFareBreakdown =
        FareBreakdownDetailEntity.builder()
            .fareId(flatFareRequest.getFareId())
            .tripId(flatFareRequest.getTripId())
            .routingDistance(flatFareRequest.getRoutingDistance())
            .ett(flatFareRequest.getEtt())
            .encodedPolyline(flatFareRequest.getEncodedPolyline())
            .calMethod(FlatfareConstants.LIVE_TRAFFIC_DYNAMIC_PRICE)
            .pickupAddressRef(flatFareRequest.getOriginAddressRef())
            .pickupAddressLat(flatFareRequest.getOriginAddressLat())
            .pickupAddressLng(flatFareRequest.getOriginAddressLng())
            .pickupZoneId(flatFareRequest.getOriginZoneId())
            .destAddressRef(flatFareRequest.getDestAddressRef())
            .destAddressLat(flatFareRequest.getDestAddressLat())
            .destAddressLng(flatFareRequest.getDestAddressLng())
            .destZoneId(flatFareRequest.getDestZoneId())
            .requestDate(new Timestamp(flatFareRequest.getRequestDate().getTime()))
            .flagDownRate(flatFareVO.getFlagDown())
            .tier1Fare(flatFareVO.getTier1Fare())
            .tier2Fare(flatFareVO.getTier2Fare())
            .waitTimeFare(flatFareVO.getWaitTimeFare())
            .peakHrFare(flatFareVO.getPeakHrFare())
            .midNightFare(flatFareVO.getMidNightFare())
            .hourlySurcharge(flatFareVO.getHourlySurcharge())
            .bookingFee(flatFareVO.getBookingFee())
            .locSurcharge(flatFareVO.getTotalLocSurCharge())
            .eventSurcharge(flatFareVO.getTotalEventSurCharge())
            .additionalSurcharge(flatFareVO.getAdditionalSurcharge())
            .multiDestSurcharge(flatFareVO.getMultiDestSurcharge())
            .dpSurgePercent(flatFareVO.getDpSurgePercent())
            .dpSurgeAmount(flatFareVO.getDpSurgeAmt())
            .dpAppliedSurgeAmount(flatFareVO.getDpAplydSurgeAmt())
            .dpBaseFareForSurge(flatFareVO.getDpBaseFareForSurge())
            .dpFinalFare(flatFareVO.getDpFinalFare())
            .dpBatchKey(flatFareVO.getBatchKey())
            .meteredBaseFare(flatFareVO.getMeteredBaseFare())
            .totalFare(flatFareVO.getTotalFare())
            .estimatedFareLF(flatFareVO.getEstimatedFareLF())
            .estimatedFareRT(flatFareVO.getEstimatedFareRT())
            .flatPlatformFeeId(flatFarePlatformFeeId)
            .flatPlatformFee(flatFarePlatformFee)
            .meterPlatformFeeId(meterPlatformFeeId)
            .meterPlatformFeeLower(meterPlatformFeeLower)
            .meterPlatformFeeUpper(meterPlatformFeeUpper)
            .areaType(requestEntity.getAreaType().getValue())
            .regionId(requestEntity.getRegionId())
            .regionVersion(requestEntity.getRegionVersion())
            .modelId(requestEntity.getModelId())
            .modelName(requestEntity.getModelName());
    if (flatFareRequest.getIntermediateAddrRef() != null) {
      streamFlatFareBreakdown
          .intermediateAddrRef(flatFareRequest.getIntermediateAddrRef())
          .intermediateAddrLat(flatFareRequest.getIntermediateAddrLat())
          .intermediateAddrLng(flatFareRequest.getIntermediateAddrLng())
          .intermediateZoneId(flatFareRequest.getIntermediateZoneId());
    }

    final FareBreakdownDetailEntity fareBreakdown = streamFlatFareBreakdown.build();

    final String keyBreakdown =
        CommonUtils.generateFareBreakdownKey(
            flatFareRequest.getFareId(), flatFareRequest.getVehTypeId());

    log.info("KEY_BREAKDOWN={}", keyBreakdown);

    cacheService.setValue(
        keyBreakdown, fareBreakdown, DateUtils.minuteToSecond(timeExpireBreakDown));
  }

  private void storeMultiFareToCache(
      final String fareId, final MultiFareResponse multiFareResponse) {
    final String keyCache = CommonUtils.generateMultiFareCacheKey(fareId);
    log.info("KEY_MULTI_FARE={},VALUE_MULTI_FARE={}", keyCache, multiFareResponse);
    cacheService.setValue(
        keyCache,
        multiFareResponse,
        DateUtils.minuteToSecond(multiFareResponse.getFareExpiredIn()));
  }

  private void storeRouteInfoToCache(final RouteInfo routeInfo, int timeExpire) {
    final String keyCache = DYNAMIC_PRICING + COLON + ROUTE + COLON + routeInfo.getTripId();
    log.info("KEY_ROUTE_INFO={}", keyCache);
    cacheService.setValue(keyCache, routeInfo, DateUtils.minuteToSecond(timeExpire));
  }

  private FlatFareVO combineEstimatedFlatFare(final EstimatedFare estimatedFare) {
    final FlatFareVO finalFlatFare;
    final boolean isLimoCalculated = estimatedFare.getFlatAndMeterFareForLimo().isCalculated();
    final boolean isDynamicPriceCalculated =
        estimatedFare.getDynamicPricingForStandard().isCalculated();

    if (isLimoCalculated) {
      finalFlatFare = estimatedFare.getFlatAndMeterFareForLimo();
    } else {
      if (isDynamicPriceCalculated) {
        finalFlatFare = estimatedFare.getDynamicPricingForStandard();
        finalFlatFare.setPdtId(FlatfareConstants.NORMAL_FLATFARE_PDT_ID);
      } else {
        finalFlatFare = estimatedFare.getMeterFareForStandard();
      }
    }
    return finalFlatFare;
  }

  private FlatFareRequest createFlatFareRequest(
      final MultiFareRequestEntity estFareRequest,
      final GenerateRouteResponse route,
      final Date requestTime,
      final String tripId,
      final String fareId) {
    return FlatFareRequest.builder()
        .fareId(fareId)
        .tripId(tripId)
        .countryCode(estFareRequest.getCountryCode())
        .mobileId(estFareRequest.getMobile())
        .jobType(estFareRequest.getJobType())
        .bookingChannel(estFareRequest.getBookingChannel())
        .originAddressRef(estFareRequest.getPickupAddressRef())
        .originAddressLat(estFareRequest.getPickupAddressLat())
        .originAddressLng(estFareRequest.getPickupAddressLng())
        .originZoneId(estFareRequest.getPickupZoneId())
        .destAddressRef(estFareRequest.getDestAddressRef())
        .destAddressLat(estFareRequest.getDestAddressLat())
        .destAddressLng(estFareRequest.getDestAddressLng())
        .destZoneId(estFareRequest.getDestZoneId())
        .intermediateAddrRef(estFareRequest.getIntermediateAddrRef())
        .intermediateAddrLat(estFareRequest.getIntermediateAddrLat())
        .intermediateAddrLng(estFareRequest.getIntermediateAddrLng())
        .intermediateZoneId(estFareRequest.getIntermediateZoneId())
        .requestDate(requestTime)
        .routingDistance(route.getDistanceMeters())
        .ett(Long.parseLong(route.getDuration()))
        .encodedPolyline(route.getEncodedPolyline())
        .vehTypeIdList(estFareRequest.getVehTypeIDs())
        .build();
  }

  private boolean isPlatformFeeEffective(
      final Date currentDate, final String effectiveFrom, final String effectiveTo) {
    try {
      final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
      final Date startDate = simpleDateFormat.parse(effectiveFrom);
      final Date endDate = simpleDateFormat.parse(effectiveTo);
      return DateUtils.isBetween(currentDate, startDate, endDate);
    } catch (ParseException exception) {
      log.error(exception.getMessage(), exception);
    }
    return false;
  }

  private PlatformFee computePlatformFee(
      final FlatFareVOPart flatFareVO,
      final FlatFareRequest flatFareRequest,
      final List<PlatformFeeResponse> platformFeeConfigList) {
    final PlatformFee platformFee = new PlatformFee();

    if (!ObjectUtils.isEmpty(platformFeeConfigList)) {
      final Date currentDate = flatFareRequest.getRequestDate();
      final List<PlatformFeeResponse> configAfterFilter =
          platformFeeConfigList.stream()
              .filter(
                  config ->
                      flatFareRequest
                              .getBookingChannel()
                              .equalsIgnoreCase(config.getBookingChannel())
                          && flatFareVO.getPdtId().equalsIgnoreCase(config.getProductId())
                          && flatFareRequest.getVehTypeId().equals(config.getVehicleGroupId())
                          && !config.getDeleted()
                          && isPlatformFeeEffective(
                              currentDate, config.getEffectiveFrom(), config.getEffectiveTo()))
              .toList();

      if (ObjectUtils.isNotEmpty(configAfterFilter) && Objects.nonNull(configAfterFilter.get(0))) {
        final PlatformFeeResponse finalConfig = configAfterFilter.get(0);
        platformFee.setConfigId(finalConfig.getId());
        switch (finalConfig.getPlatformFeeApplicability()) {
          case APPLICABLE_TYPE_NO, APPLICABLE_TYPE_WAIVE -> setPlatformFeeZero(platformFee);
          case APPLICABLE_TYPE_YES -> setPlatformFeeLowerAndHigher(
              flatFareVO, platformFee, finalConfig, flatFareRequest);
          default -> log.error("Error to get ApplicableType of platform fee response");
        }
      }
    }
    return platformFee;
  }

  private void setPlatformFeeZero(final PlatformFee platformFee) {
    platformFee.setPlatformFeeLower(FlatfareConstants.PLATFORM_FEE_DEFAULT);
    platformFee.setPlatformFeeUpper(FlatfareConstants.PLATFORM_FEE_DEFAULT);
  }

  private void setPlatformFeeLowerAndHigher(
      final FlatFareVOPart flatFareVO,
      final PlatformFee platformFee,
      final PlatformFeeResponse configResponse,
      final FlatFareRequest flatFareRequest) {
    final double fareLF = flatFareVO.getEstimatedFareLF().doubleValue();
    final double fareRT = flatFareVO.getEstimatedFareRT().doubleValue();
    final Double threshold = configResponse.getPlatformFeeThresholdLimit();
    final double totalFare = flatFareVO.getTotalFare().doubleValue();

    if (FlatfareConstants.STD_PDT_ID.equalsIgnoreCase(flatFareVO.getPdtId())) {
      if (JobTypeEnum.IMMEDIATE.getValue().equalsIgnoreCase(flatFareRequest.getJobType())) {
        // NGPME-9582: change to use medianFare for meter
        final double medianFare = (fareLF + fareRT) / 2;
        final double platformFeeMeter =
            medianFare < threshold
                ? configResponse.getLowerPlatformFee()
                : configResponse.getUpperPlatformFee();
        platformFee.setPlatformFeeLower(platformFeeMeter);
        platformFee.setPlatformFeeUpper(platformFeeMeter);
      } else if (JobTypeEnum.ADVANCE.getValue().equalsIgnoreCase(flatFareRequest.getJobType())) {
        // NGPME-9789: update AJ lower price to upper price
        platformFee.setPlatformFeeLower(configResponse.getUpperPlatformFee());
        platformFee.setPlatformFeeUpper(configResponse.getUpperPlatformFee());
      }
    } else {
      platformFee.setPlatformFeeLower(
          totalFare < threshold
              ? configResponse.getLowerPlatformFee()
              : configResponse.getUpperPlatformFee());
      platformFee.setPlatformFeeUpper(platformFee.getPlatformFeeLower());
    }
  }

  private MultiFareResponse createEstimatedFareResponse(
      final FlatFareRequest flatFareRequest,
      final List<FlatFareVOPart> flatFareVOPartList,
      Date calculatedDate,
      int timeExpireMultiFare,
      String fareId) {
    return MultiFareResponse.builder()
        .fareId(fareId)
        .pickupAddressRef(flatFareRequest.getOriginAddressRef())
        .destAddressRef(flatFareRequest.getDestAddressRef())
        .intermediateAddrRef(flatFareRequest.getIntermediateAddrRef())
        .mobile(flatFareRequest.getMobileId())
        .countryCode(flatFareRequest.getCountryCode())
        .fareCalcTime(flatFareRequest.getRequestDate().toInstant().atOffset(ZoneOffset.UTC))
        .estimatedTripTime(flatFareRequest.getEtt())
        .distance(flatFareRequest.getRoutingDistance())
        .encodedPolyline(flatFareRequest.getEncodedPolyline())
        .tripId(flatFareRequest.getTripId())
        .fareExpiredIn(timeExpireMultiFare)
        .calculatedDate(calculatedDate)
        .flatFareVOParts(flatFareVOPartList)
        .build();
  }

  @Override
  public StoreFareBreakdownCommandResponse storeFareBreakdownDetail(
      StoreFareBreakdownCommandRequest queryRequest) {
    final StoreFareBreakdownRequestEntity requestEntity =
        domainMapper.mapToStoreFareBreakdownRequestEntity(queryRequest);

    final String fareBreakdownKey =
        CommonUtils.generateFareBreakdownKey(
            requestEntity.getFareId(), requestEntity.getVehicleTypeId());
    log.info("fareBreakdownKey={}", fareBreakdownKey);

    final FareBreakdownDetailEntity fareBreakdownData =
        cacheService.getValue(fareBreakdownKey, FareBreakdownDetailEntity.class);
    log.info("fareBreakdownData={}", fareBreakdownData);

    if (fareBreakdownData == null) {
      String errorMsg = String.format("Not found fare breakdown in cache key=%s", fareBreakdownKey);
      log.error(errorMsg);
      throw new NotFoundException(
          GET_FARE_BREAK_DOWN_ERROR.getMessage(), GET_FARE_BREAK_DOWN_ERROR.getErrorCode());
    }

    fareBreakdownData.setBookingId(requestEntity.getBookingId());
    fareBreakdownData.setFareId(requestEntity.getFareId());

    final boolean isExisted = flatFareBreakDownRepository.isExisted(requestEntity.getBookingId());
    if (isExisted) {
      log.error("fare breakdown already existed: {}", requestEntity.getFareId());
      throw new BadRequestException(
          FARE_BREAKDOWN_EXISTED.getMessage(), FARE_BREAKDOWN_EXISTED.getErrorCode());
    }

    flatFareBreakDownRepository.createFareBreakdown(fareBreakdownData);

    saveMlCreateBookingRequestAggStatsAsync(requestEntity, fareBreakdownData);

    return StoreFareBreakdownCommandResponse.builder().success(Boolean.TRUE).build();
  }

  private void saveMlCreateBookingRequestAggStatsAsync(
      final StoreFareBreakdownRequestEntity requestEntity,
      final FareBreakdownDetailEntity fareBreakdownData) {
    CompletableFuture.runAsync(
            () -> {
              final MultiFareResponse fareInCache =
                  getMultiFareResponseFromCache(requestEntity.getFareId());
              MlCreateBookingRequestAggStatsEntity entity =
                  mlCreateBookingRequestAggStatsMapper.mapToMlCreateBookingRequestAggStatsEntity(
                      fareBreakdownData, fareInCache);
              mlCreateBookingRequestAggStatsRepository.save(entity);
              if (log.isDebugEnabled()) {
                log.debug(
                    "[saveMlCreateBookingRequestAggStatsAsync] Saved breakdown to ML stats successfully for fareId: {}, bookingId: {}, vehicleTypeId: {}",
                    requestEntity.getFareId(),
                    requestEntity.getBookingId(),
                    requestEntity.getVehicleTypeId());
              }
            })
        .exceptionally(
            throwable -> {
              log.error(
                  "[saveMlCreateBookingRequestAggStatsAsync] Failed to save breakdown to ML stats for fareId: {}, bookingId: {}, vehicleTypeId: {}",
                  requestEntity.getFareId(),
                  requestEntity.getBookingId(),
                  requestEntity.getVehicleTypeId(),
                  throwable);
              return null;
            });
  }
}
