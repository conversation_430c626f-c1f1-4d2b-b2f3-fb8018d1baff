package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.impl;

import static com.cdg.pmg.ngp.me.dynamicpricing.constants.RedisKeyConstant.CACHE_KEY_H3_REGION_SURGE_MODEL_PREFIX;

import com.cdg.pmg.ngp.me.dynamicpricing.annotations.ServiceComponent;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.FlatFareHoliday;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeComputationRequest;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.dtos.SurgeComputationResponse;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.mapper.surgecomputation.SurgeFactorCalculationMapper;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.FlatFareConfigService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.StandardInputService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.SurgeFactorCalculationService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.ConfigurationContext;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.ConfigurationProviderFactory;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.factory.dataprovider.ConfigurationDataProvider;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.services.surgecomputation.farecounter.RequestCounterService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.inbound.utils.DateUtils;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.dtos.H3Region;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation.*;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.AddressService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.CacheService;
import com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.services.ConfigurationServiceOutboundPort;
import com.cdg.pmg.ngp.me.dynamicpricing.constants.RequestCountConstant;
import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.*;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.ErrorEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.enums.MappingTypeEnum;
import com.cdg.pmg.ngp.me.dynamicpricing.exceptions.InternalServerException;
import com.cdg.pmg.ngp.me.dynamicpricing.utils.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * Implementation of the SurgeFactorCalculationService interface. This class provides functionality
 * to trigger the calculation of surge factors across all regions.
 */
@ServiceComponent
@RequiredArgsConstructor
@Slf4j
public class SurgeFactorCalculationServiceImpl implements SurgeFactorCalculationService {

  // Service dependencies
  private final AddressService addressService;
  private final ModelRepository modelRepository;
  private final StaticTimeBasedConfigurationRepository staticTimeBasedConfigurationRepository;
  private final StaticRegionBasedConfigurationRepository staticRegionBasedConfigurationRepository;
  private final SurgeComputationModelApiLogRepository surgeComputationModelApiLogRepository;
  private final SurgeComputationModelSurgeRepository surgeComputationModelSurgeRepository;
  private final StandardInputService standardInputService;
  private final FlatFareConfigService flatFareConfigService;
  private final ObjectMapper objectMapper;
  private final HttpClient httpClient;
  private final CacheService cacheService;
  private final SurgeFactorCalculationMapper surgeFactorCalculationMapper;
  private final ConfigurationServiceOutboundPort configurationServiceOutboundPort;
  private final RequestCounterService requestCounterService;
  private final MlCreateBookingRequestAggStatsRepository mlCreateBookingRequestAggStatsRepository;

  @Value("${spring.cloud.openfeign.client.config.surgeComputationModelClient.url}")
  private String surgeComputationModelDomain;

  // Configuration constants
  private static final int HTTP_REQUEST_TIMEOUT_SECONDS = 10;
  private static final int MAX_HTTP_CONCURRENCY = 20;

  // Thread pools for async operations
  private final ExecutorService httpExecutor =
      new ThreadPoolExecutor(
          MAX_HTTP_CONCURRENCY / 2,
          MAX_HTTP_CONCURRENCY,
          60L,
          TimeUnit.SECONDS,
          new LinkedBlockingQueue<>(MAX_HTTP_CONCURRENCY * 2),
          new ThreadPoolExecutor.AbortPolicy());

  /**
   * Triggers the calculation of surge factors across all regions. This is an asynchronous operation
   * that initiates the calculation process and returns a CompletableFuture for tracking completion.
   *
   * @return CompletableFuture containing the calculation result status
   */
  @Override
  public boolean calculateSurgeFactor(Instant triggerTime) {
    return performCalculation(triggerTime);
  }

  @Override
  public List<H3RegionSurgeEntity> getH3RegionSurge(final String modelName) {
    String cacheKey = CACHE_KEY_H3_REGION_SURGE_MODEL_PREFIX + modelName;
    List<H3RegionSurgeEntity> listValue =
        cacheService.getListValue(cacheKey, H3RegionSurgeEntity.class);

    if (CollectionUtils.isNotEmpty(listValue)) {
      return listValue;
    }

    List<SurgeComputationModelSurgeEntity> surges =
        surgeComputationModelSurgeRepository.findByModelName(modelName);
    List<H3RegionSurgeEntity> h3RegionSurgeEntities =
        surgeFactorCalculationMapper.mapFromSurgeComputationModelSurgeEntity(surges);
    if (CollectionUtils.isNotEmpty(h3RegionSurgeEntities)) {
      cacheService.setListValue(cacheKey, h3RegionSurgeEntities);
    }
    return h3RegionSurgeEntities;
  }

  @Override
  public List<ModelSurgeDataEntity> getCurrentSurgeValuesFromCache() {
    log.debug("[getCurrentSurgeValuesFromCache] Retrieving current surge values from cache");

    try {
      // Get all active models
      List<ModelEntity> models = modelRepository.findAll();
      if (models.isEmpty()) {
        log.warn("[getCurrentSurgeValuesFromCache] No active models found");
        return List.of();
      }

      List<ModelSurgeDataEntity> result = new ArrayList<>(models.size());

      for (ModelEntity model : models) {
        ModelSurgeDataEntity modelSurgeData = createModelSurgeDataFromCache(model);
        result.add(modelSurgeData);
      }

      log.debug(
          "[getCurrentSurgeValuesFromCache] Retrieved surge data for {} models", result.size());
      return result;

    } catch (Exception e) {
      log.error(
          "[getCurrentSurgeValuesFromCache] Error retrieving surge values from cache: {}",
          e.getMessage(),
          e);
      return List.of();
    }
  }

  @Override
  public PagedSurgeValuesResult getCurrentSurgeValuesFromCache(
      SurgeValuesFilterCriteria filterCriteria) {
    log.debug(
        "[getCurrentSurgeValuesFromCache] Retrieving current surge values from cache with pagination - page: {}, size: {}",
        filterCriteria.getPage(),
        filterCriteria.getSize());

    try {
      // Get filtered models with pagination
      List<ModelEntity> models = modelRepository.findWithFilter(filterCriteria);
      long totalElements = modelRepository.countWithFilter(filterCriteria);

      if (models.isEmpty()) {
        log.warn("[getCurrentSurgeValuesFromCache] No models found for given criteria");
        return PagedSurgeValuesResult.of(
            List.of(), filterCriteria.getPage(), filterCriteria.getSize(), totalElements);
      }

      List<ModelSurgeDataEntity> result = new ArrayList<>(models.size());

      for (ModelEntity model : models) {
        ModelSurgeDataEntity modelSurgeData = createModelSurgeDataFromCache(model);
        result.add(modelSurgeData);
      }

      PagedSurgeValuesResult pagedResult =
          PagedSurgeValuesResult.of(
              result, filterCriteria.getPage(), filterCriteria.getSize(), totalElements);

      log.debug(
          "[getCurrentSurgeValuesFromCache] Retrieved surge data for {} models (page {}/{})",
          result.size(),
          pagedResult.getPage() + 1,
          pagedResult.getTotalPages());

      return pagedResult;

    } catch (Exception e) {
      log.error(
          "[getCurrentSurgeValuesFromCache] Error retrieving surge values from cache: {}",
          e.getMessage(),
          e);
      return PagedSurgeValuesResult.of(
          List.of(), filterCriteria.getPage(), filterCriteria.getSize(), 0);
    }
  }

  /**
   * Creates ModelSurgeDataEntity for a specific model by retrieving data from cache only. This
   * method is optimized for monitoring calls and avoids database operations.
   */
  private ModelSurgeDataEntity createModelSurgeDataFromCache(ModelEntity model) {
    String cacheKey = CACHE_KEY_H3_REGION_SURGE_MODEL_PREFIX + model.getModelName();

    try {
      // Try to get surge data from cache only
      List<H3RegionSurgeEntity> regions =
          cacheService.getListValue(cacheKey, H3RegionSurgeEntity.class);

      if (CollectionUtils.isEmpty(regions)) {
        regions = List.of(); // Return empty list instead of null
      }

      return ModelSurgeDataEntity.builder()
          .modelId(model.getId())
          .modelName(model.getModelName())
          .regions(regions)
          .build();

    } catch (Exception e) {
      log.warn(
          "[createModelSurgeDataFromCache] Error retrieving cache data for model {}: {}",
          model.getModelName(),
          e.getMessage());

      return ModelSurgeDataEntity.builder()
          .modelId(model.getId())
          .modelName(model.getModelName())
          .regions(List.of())
          .build();
    }
  }

  /**
   * Performs the actual surge factor calculation.
   *
   * @return true if calculation completed successfully, false otherwise
   */
  private boolean performCalculation(Instant triggerTime) {
    log.info("[calculateSurgeFactor] Performing surge factor calculation");

    try {
      DynamicPricingSurgeConfig dynamicPricingSurgeConfig =
          configurationServiceOutboundPort.getDynamicPricingSurgeConfig();
      DynamicPricingSurgeSamplingPeriod samplingPeriod =
          dynamicPricingSurgeConfig.getSamplingPeriod();
      Instant startTime = triggerTime.minus(Duration.ofMinutes(samplingPeriod.getFrom()));
      Instant endTime = triggerTime.minus(Duration.ofMinutes(samplingPeriod.getTo()));
      log.info(
          "[calculateSurgeFactor] Performing surge factor calculation, startTime: {}, endTime: {}",
          startTime,
          endTime);

      // 1. Get standard input value async
      CompletableFuture<Map<Long, Map<String, BigDecimal>>> standardInputValueMapFuture =
          getStandardInputValueAsync(startTime, endTime);

      // 2. Retrieve all effective h3 regions
      List<H3Region> regions = addressService.getEffectiveH3RegionsFromCache();
      if (regions.isEmpty()) {
        log.error("[calculateSurgeFactor] Failed to retrieve effective h3 regions");
        return false;
      }

      // 3. Retrieve all active models
      List<ModelEntity> models = modelRepository.findAll();
      if (models.isEmpty()) {
        log.error("[calculateSurgeFactor] There is no active model");
        return false;
      }

      // 4. Set up the configuration provider factory
      ConfigurationProviderFactory configProviderFactory =
          setupConfigurationFactory(triggerTime, models, standardInputValueMapFuture);

      // 5. Get the fare request count in the specified time range
      Map<Long, Long> regionFareRequestCount = getFareRequestCount(startTime, endTime);

      // 6. Build requests for each model and region
      List<SurgeComputationRequest> requests =
          buildRequests(
              triggerTime, regions, models, regionFareRequestCount, configProviderFactory);
      log.info(
          "[calculateSurgeFactor] Built {} requests for {} models and {} regions",
          requests.size(),
          models.size(),
          regions.size());

      // 7. Send requests to surge computation model service and process responses
      boolean success = processHttpRequests(requests);

      log.info(
          "[calculateSurgeFactor] Surge factor calculation completed in {} seconds",
          Duration.between(triggerTime, Instant.now()).getSeconds());

      return success;
    } catch (Exception e) {
      log.error(
          "[calculateSurgeFactor] Error during surge factor calculation: {}", e.getMessage(), e);
      return false;
    }
  }

  private CompletableFuture<Map<Long, Map<String, BigDecimal>>> getStandardInputValueAsync(
      final Instant startTime, final Instant endTime) {

    return CompletableFuture.supplyAsync(
            () -> {
              log.info("[calculateSurgeFactor] Start getting standard input value async");
              List<String> bookingIds =
                  mlCreateBookingRequestAggStatsRepository.getBookingIdsInTimeRange(
                      startTime, endTime);

              // Retrieve standard input values
              Map<Long, Map<String, BigDecimal>> standardInputValueMap =
                  standardInputService.getStandardInputValueMap(bookingIds);

              if (log.isDebugEnabled()) {
                log.debug(
                    "[calculateSurgeFactor] bookingIds: {}, startTime: {}, endTime: {}",
                    bookingIds,
                    startTime,
                    endTime);
                log.debug(
                    "[calculateSurgeFactor] Retrieved standard input values {}",
                    standardInputValueMap);
              }
              return standardInputValueMap;
            })
        .orTimeout(HTTP_REQUEST_TIMEOUT_SECONDS * 2, TimeUnit.SECONDS)
        .exceptionally(
            ex -> {
              if (ex instanceof TimeoutException) {
                log.error(
                    "[calculateSurgeFactor] Getting standard input value timed out after {} seconds",
                    HTTP_REQUEST_TIMEOUT_SECONDS * 2);
              } else {
                log.error(
                    "[calculateSurgeFactor] Error Getting standard input value, {}",
                    ex.getMessage(),
                    ex);
              }
              return Map.of();
            });
  }

  /**
   * Sets up the configuration provider factory with all necessary configurations.
   *
   * @param triggerTime Current timestamp
   * @param models List of active models
   * @param standardInputValueMapFuture the standard input value map future
   * @return Configured ConfigurationProviderFactory
   */
  private ConfigurationProviderFactory setupConfigurationFactory(
      final Instant triggerTime,
      final List<ModelEntity> models,
      final CompletableFuture<Map<Long, Map<String, BigDecimal>>> standardInputValueMapFuture) {

    // Retrieve static time based configurations
    List<StaticTimeBasedConfigurationEntity> staticTimeBasedConfigs =
        getTimeBasedConfig(triggerTime);

    // Retrieve static region based configurations
    // Because each model has its own region based configuration, we loop retrieve it here.
    Map<String, Map<Long, String>> staticRegionBasedConfigMap =
        getRegionBasedConfigMap(triggerTime, models);

    // Check if today is holiday
    boolean isHoliday = isHoliday(triggerTime);

    Map<Long, Map<String, BigDecimal>> standardInputValueMap =
        getStandardInputValueMap(standardInputValueMapFuture);

    ConfigurationProviderFactory factory =
        new ConfigurationProviderFactory(
            staticTimeBasedConfigs, staticRegionBasedConfigMap, standardInputValueMap, isHoliday);

    // Validate that all required providers are available
    validateConfigurationProviders(models, factory);

    return factory;
  }

  private List<StaticTimeBasedConfigurationEntity> getTimeBasedConfig(final Instant triggerTime) {
    List<StaticTimeBasedConfigurationEntity> staticTimeBasedConfigs =
        staticTimeBasedConfigurationRepository.findByEffectiveTimeRange(triggerTime);
    if (log.isDebugEnabled()) {
      log.debug(
          "[calculateSurgeFactor] Retrieved {} static time-based configurations",
          staticTimeBasedConfigs.size());
    }

    return staticTimeBasedConfigs;
  }

  private Map<Long, Map<String, BigDecimal>> getStandardInputValueMap(
      final CompletableFuture<Map<Long, Map<String, BigDecimal>>> standardInputValueMapFuture) {
    Map<Long, Map<String, BigDecimal>> standardInputValueMap = standardInputValueMapFuture.join();
    if (standardInputValueMap == null || standardInputValueMap.isEmpty()) {
      log.error("[calculateSurgeFactor] Region demand supply statistics are empty");
      throw new InternalServerException(
          ErrorEnum.REGION_DEMAND_SUPPLY_STATISTICS_ERROR.getMessage(),
          ErrorEnum.REGION_DEMAND_SUPPLY_STATISTICS_ERROR.getErrorCode());
    }
    return standardInputValueMap;
  }

  private boolean isHoliday(final Instant triggerTime) {
    List<FlatFareHoliday> holidayList = flatFareConfigService.getListHoliday();
    boolean isHoliday = DateUtils.isHolidaySingTime(Date.from(triggerTime), holidayList);
    log.info("[calculateSurgeFactor] Current date holiday status: {}", isHoliday);
    return isHoliday;
  }

  private Map<String, Map<Long, String>> getRegionBasedConfigMap(
      final Instant triggerTime, final List<ModelEntity> models) {
    Map<String, Map<Long, String>> staticRegionBasedConfigMap = new HashMap<>();
    for (final ModelEntity model : models) {
      staticRegionBasedConfigMap.putAll(
          staticRegionBasedConfigurationRepository.findMapByEffectiveTimeRange(
              model.getId(), triggerTime));
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[calculateSurgeFactor] Retrieved static region-based configurations for {} configuration types",
          staticRegionBasedConfigMap.size());
    }
    return staticRegionBasedConfigMap;
  }

  /**
   * Validates that all required configuration providers are available for the models.
   *
   * @param models List of active models
   * @param factory ConfigurationProviderFactory to validate
   * @throws InternalServerException if any required provider is missing
   */
  private void validateConfigurationProviders(
      List<ModelEntity> models, ConfigurationProviderFactory factory) {

    Set<MappingTypeEnum> mappingTypes =
        models.stream()
            .map(ModelEntity::getRequestFieldsMappings)
            .flatMap(Collection::stream)
            .map(RequestFieldMapping::getMappingType)
            .collect(Collectors.toSet());

    for (MappingTypeEnum mappingType : mappingTypes) {
      if (factory.getProvider(mappingType) == null) {
        log.error(
            "[calculateSurgeFactor] Failed to get configuration provider for mapping type {}",
            mappingType);
        throw new InternalServerException(
            ErrorEnum.NOT_FOUND_SURGE_COMPUTATION_MAPPING_TYPE.getMessage(),
            ErrorEnum.NOT_FOUND_SURGE_COMPUTATION_MAPPING_TYPE.getErrorCode());
      }
    }

    if (log.isDebugEnabled()) {
      log.debug(
          "[calculateSurgeFactor] Successfully validated all {} configuration providers",
          mappingTypes.size());
    }
  }

  /**
   * Builds requests for each model and region combination.
   *
   * @param now Current timestamp
   * @param regions List of H3 regions
   * @param models List of active models
   * @param configFactory Configuration provider factory
   * @return List of surge computation requests
   */
  private List<SurgeComputationRequest> buildRequests(
      final Instant now,
      final List<H3Region> regions,
      final List<ModelEntity> models,
      final Map<Long, Long> regionFareRequestCount,
      final ConfigurationProviderFactory configFactory) {

    List<SurgeComputationRequest> requests = new ArrayList<>(models.size());

    for (final ModelEntity model : models) {
      List<Map<String, BigDecimal>> regionParams =
          createRegionParameters(regions, model, now, configFactory, regionFareRequestCount);

      SurgeComputationRequest.HttpRequest httpRequest = new SurgeComputationRequest.HttpRequest();
      httpRequest.setRequest_id(UUID.randomUUID().toString());
      httpRequest.setModel_name(model.getModelName());
      httpRequest.setRequest_time(OffsetDateTime.now());
      httpRequest.setRegions(regionParams);

      SurgeComputationRequest request =
          new SurgeComputationRequest(
              model.getId(), model.getModelName(), model.getEndpointUrl(), httpRequest);

      requests.add(request);
      if (log.isDebugEnabled()) {
        log.debug(
            "[calculateSurgeFactor] Built request for model: {}, with {} region parameters",
            model.getModelName(),
            regionParams.size());
      }
    }

    return requests;
  }

  /**
   * Get the fare request count in the specified time range
   *
   * @param startTime start time
   * @param endTime end time
   * @return The fare request count based on region
   */
  private Map<Long, Long> getFareRequestCount(final Instant startTime, final Instant endTime) {
    return requestCounterService.getRequestCountByRegions(
        RequestCountConstant.MULTI_FARE, startTime, endTime);
  }

  /**
   * Creates parameters for each region in a model request.
   *
   * @param regions List of H3 regions
   * @param model Model entity
   * @param now Current timestamp
   * @param configFactory Configuration provider factory
   * @param regionFareRequestCount Number of multi-fare requests based on region
   * @return List of region parameter maps
   */
  private List<Map<String, BigDecimal>> createRegionParameters(
      List<H3Region> regions,
      ModelEntity model,
      Instant now,
      ConfigurationProviderFactory configFactory,
      Map<Long, Long> regionFareRequestCount) {

    // The region surge of the last calculation
    Map<Long, BigDecimal> regionSurgeMap = getH3RegionSurgeMap(model.getModelName());

    List<Map<String, BigDecimal>> regionParams = new ArrayList<>(regions.size());

    for (final H3Region region : regions) {
      Map<String, BigDecimal> regionParam = new HashMap<>();
      regionParam.put(
          SurgeComputationRequest.H3_REGION_ID_KEY, BigDecimal.valueOf(region.getRegionId()));
      regionParam.put(
          SurgeComputationRequest.CURRENT_SURGE,
          regionSurgeMap.getOrDefault(region.getRegionId(), BigDecimal.ZERO));

      BigDecimal multiFareRequestCount =
          BigDecimal.valueOf(regionFareRequestCount.getOrDefault(region.getRegionId(), 0L));
      regionParam.put(SurgeComputationRequest.GET_FARE_COUNT, multiFareRequestCount);

      for (final RequestFieldMapping fieldMapping : model.getRequestFieldsMappings()) {
        ConfigurationDataProvider provider =
            configFactory.getProvider(fieldMapping.getMappingType());

        ConfigurationContext context =
            new ConfigurationContext(
                fieldMapping.getMappingConfigurationName(), now, region.getRegionId());

        regionParam.put(
            fieldMapping.getRequestParameterName(), provider.getConfigurationData(context));
      }

      logWarningIfGetFareCountLessThanDeman(regionParam, multiFareRequestCount);

      regionParams.add(regionParam);
    }

    return regionParams;
  }

  private static void logWarningIfGetFareCountLessThanDeman(
      final Map<String, BigDecimal> regionParam, final BigDecimal multiFareRequestCount) {
    BigDecimal comfortRideDemand =
        regionParam.getOrDefault(SurgeComputationRequest.COMFORT_RIDE_DEMAND, BigDecimal.ZERO);
    BigDecimal meterDemand =
        regionParam.getOrDefault(SurgeComputationRequest.METER_DEMAND, BigDecimal.ZERO);
    if (multiFareRequestCount == null || comfortRideDemand == null || meterDemand == null) {
      return;
    }

    if (multiFareRequestCount.compareTo(comfortRideDemand.add(meterDemand)) < 0) {
      log.warn(
          "[calculateSurgeFactor] get_fare_count is less than comfort_ride_demand + meter_demand, regionId: {}, get_fare_count: {}, comfort_ride_demand: {}, meter_demand: {}",
          regionParam.get(SurgeComputationRequest.H3_REGION_ID_KEY),
          multiFareRequestCount,
          comfortRideDemand,
          meterDemand);
    }
  }

  /**
   * Processes HTTP requests for surge computation.
   *
   * @param requests List of surge computation requests
   * @return true if all requests were processed successfully, false otherwise
   */
  private boolean processHttpRequests(List<SurgeComputationRequest> requests) {
    try {
      // Create a list of CompletableFuture for each HTTP request
      List<CompletableFuture<Optional<SurgeComputationResponse>>> responseFutures =
          requests.stream().map(this::doHttpRequestAsync).toList();

      // Asynchronously combine all requests and process the results
      return CompletableFuture.allOf(responseFutures.toArray(new CompletableFuture[0]))
          .thenApply(
              v -> {
                // Process successful responses and store results
                long successCount = 0;
                for (CompletableFuture<Optional<SurgeComputationResponse>> future :
                    responseFutures) {
                  Optional<SurgeComputationResponse> responseOpt = future.join();
                  if (responseOpt.isPresent()) {
                    storeResult(responseOpt.get());
                    successCount++;
                  }
                }

                log.info(
                    "[calculateSurgeFactor] Successfully processed {}/{} HTTP responses",
                    successCount,
                    requests.size());
                return successCount == requests.size();
              })
          .orTimeout(HTTP_REQUEST_TIMEOUT_SECONDS * 2, TimeUnit.SECONDS)
          .exceptionally(
              ex -> {
                if (ex instanceof TimeoutException) {
                  log.error(
                      "[calculateSurgeFactor] Processing HTTP responses timed out after {} seconds",
                      HTTP_REQUEST_TIMEOUT_SECONDS * 2);
                } else {
                  log.error(
                      "[calculateSurgeFactor] Error while processing HTTP responses, {}",
                      ex.getMessage(),
                      ex);
                }
                return false;
              })
          .join();
    } catch (Exception e) {
      log.error("[calculateSurgeFactor] Unexpected error during HTTP request processing", e);
      return false;
    }
  }

  /**
   * Executes an HTTP request asynchronously.
   *
   * @param request Surge computation request
   * @return CompletableFuture containing the optional response
   */
  private CompletableFuture<Optional<SurgeComputationResponse>> doHttpRequestAsync(
      final SurgeComputationRequest request) {

    return CompletableFuture.supplyAsync(
        () -> {
          String endpointUrl = getEndpointUrl(request);
          try {
            String requestBody = objectMapper.writeValueAsString(request.getHttpRequest());

            HttpRequest httpRequest =
                HttpRequest.newBuilder()
                    .uri(URI.create(endpointUrl))
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofSeconds(HTTP_REQUEST_TIMEOUT_SECONDS))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            HttpResponse<String> response =
                httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

            // Store request log into db
            storeRequestLog(request, response, requestBody);

            if (response.statusCode() != 200) {
              log.error(
                  "[calculateSurgeFactor] Error during HTTP request, URL: {}, status: {}, response: {}",
                  endpointUrl,
                  response.statusCode(),
                  response.body());
              return Optional.empty();
            }

            if (log.isDebugEnabled()) {
              log.debug("[calculateSurgeFactor] Successful HTTP request to {}", endpointUrl);
            }

            SurgeComputationResponse.HttpResponse httpResponse =
                objectMapper.readValue(
                    response.body(), SurgeComputationResponse.HttpResponse.class);
            return Optional.of(
                new SurgeComputationResponse(
                    request.getModelId(), request.getModelName(), endpointUrl, httpResponse));
          } catch (Exception e) {
            log.error(
                "[calculateSurgeFactor] Failed to execute HTTP request to {}: {}",
                endpointUrl,
                e.getMessage(),
                e);
            return Optional.empty();
          }
        },
        httpExecutor);
  }

  /**
   * Get the whole surge computation model service API
   *
   * @param request The request
   * @return The whole surge computation model service API
   */
  private String getEndpointUrl(final SurgeComputationRequest request) {
    String endpointUrl = request.getEndpointUrl();
    if (!endpointUrl.startsWith("/")) {
      endpointUrl = "/" + endpointUrl;
    }
    endpointUrl = surgeComputationModelDomain + endpointUrl;
    return endpointUrl;
  }

  /**
   * Store request log into db
   *
   * @param request The request
   * @param response The response
   * @param requestBody The request params
   */
  private void storeRequestLog(
      final SurgeComputationRequest request,
      final HttpResponse<String> response,
      final String requestBody) {
    surgeComputationModelApiLogRepository.save(
        SurgeComputationModelApiLogEntity.builder()
            .modelId(request.getModelId())
            .modelName(request.getModelName())
            .endpointUrl(request.getEndpointUrl())
            .createTimestamp(Instant.now())
            .statusCode(response.statusCode())
            .requestParams(requestBody)
            .responseBody(response.body())
            .build());
  }

  /**
   * Stores the result of a surge computation response in cache and database.
   *
   * @param response Surge computation response
   */
  private void storeResult(SurgeComputationResponse response) {
    try {
      if (log.isDebugEnabled()) {
        log.debug("[calculateSurgeFactor] Storing result for model: {}", response.getModelName());
      }
      // Here you would implement the logic to store the result in Redis and PostgreSQL
      List<H3RegionSurgeEntity> h3RegionSurges =
          surgeFactorCalculationMapper.mapToH3RegionSurgeEntity(
              response.getResponse().getResults());

      surgeComputationModelSurgeRepository.batchUpsertSurges(response.getModelId(), h3RegionSurges);

      String cacheKey = CACHE_KEY_H3_REGION_SURGE_MODEL_PREFIX + response.getModelName();
      cacheService.deleteByKey(cacheKey);
      cacheService.setListValue(cacheKey, h3RegionSurges);
      cacheService.setExpire(cacheKey, 300); // 5 minutes

      if (log.isDebugEnabled()) {
        log.debug(
            "[calculateSurgeFactor] Successfully stored result for model: {}",
            response.getModelName());
      }
    } catch (Exception e) {
      log.error(
          "[calculateSurgeFactor] Failed to store result for model {}: {}",
          response.getModelName(),
          e.getMessage(),
          e);
    }
  }

  /** Cleans up resources when the service is being destroyed. */
  @PreDestroy
  public void cleanup() {
    log.info("[calculateSurgeFactor] Shutting down SurgeFactorCalculationService thread pools");

    shutdownExecutor(httpExecutor);

    log.info("[calculateSurgeFactor] SurgeFactorCalculationService cleanup completed");
  }

  /**
   * Safely shuts down an executor service.
   *
   * @param executor Executor service to shut down
   */
  private void shutdownExecutor(ExecutorService executor) {
    executor.shutdown();
    try {
      if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
        log.warn(
            "[calculateSurgeFactor] HTTP Executor did not terminate in time, forcing shutdown");
        List<Runnable> droppedTasks = executor.shutdownNow();
        log.warn("[calculateSurgeFactor] HTTP Executor dropped {} tasks", droppedTasks.size());

        if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
          log.error("[calculateSurgeFactor] HTTP Executor still did not terminate");
        }
      }
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.warn("[calculateSurgeFactor] HTTP Executor shutdown interrupted");
      executor.shutdownNow();
    }
  }
}
