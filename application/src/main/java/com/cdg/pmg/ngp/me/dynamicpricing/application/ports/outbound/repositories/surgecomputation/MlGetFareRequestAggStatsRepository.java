package com.cdg.pmg.ngp.me.dynamicpricing.application.ports.outbound.repositories.surgecomputation;

import com.cdg.pmg.ngp.me.dynamicpricing.entities.surgecomputation.MlGetFareRequestAggStatsEntity;
import java.util.List;

/**
 * Repository interface for ML Get Fare Request Aggregation Statistics. Provides methods to interact
 * with the ml_get_fare_request_agg_stats table.
 */
public interface MlGetFareRequestAggStatsRepository {

  /**
   * Batch save ML Get Fare Request Aggregation Statistics entities.
   *
   * @param entities a list of {@link MlGetFareRequestAggStatsEntity} to save
   */
  void saveAll(List<MlGetFareRequestAggStatsEntity> entities);
}
