@startuml multi-fare-request-count

title <b>Surge Computation Count Flow
skinparam {
    SequenceGroupFontSize 14
    NoteBackgroundColor #FFF9C4
}

autonumber
actor EventBridge <<Trigger>>
participant ngp_me_dynamicpricing_svc as pricing
participant ngp_me_fleetanalytic_svc as fleet
participant ngp_me_address_svc as address
participant ngp_me_weather_svc as weather
participant ngp_me_surge_computation_model_svc as surge
database Postgres
participant Redis

' ========== MAIN FLOW ==========
activate pricing

EventBridge -> pricing : [POST] '/v1.0/surge-computation/calculate-surge-factor'

' ----- Fare Count Calculation -----
opt#LightBlue Calculate Get Fare Count Detail
    note over pricing
        Async storage of fare count metrics
    end note
    pricing -> Redis++ : get 'multi-fare' API request count
    return fare count data

    pricing -> Postgres++ : store in **'ml_get_fare_request_agg_stats'**
end

' ----- Core Surge Calculation -----
opt#LightBlue Surge Computation Process

    ' -- Standard Inputs --
    alt Get Standard Inputs (Async)
        pricing -> fleet++ : [POST] '/v1.0/fleet-analytic/region/cal-demand-supply'
        return demand list

        pricing -> weather++ : [POST] '/v1.0/rainfall/fetch'
        return rainfall data
    end

    ' -- H3 Regions --
    alt Get Effective H3 Regions
        pricing -> pricing : check local cache
        alt if cache miss
            pricing -> address++ : [GET] '/v1.0/h3-regions/definition/search/effective'
            return h3 regions
            pricing -> pricing : cache for 5 mins
        end
    else If no regions
        pricing -> EventBridge : return 500 error
    end

    ' -- Models --
    alt Get All models
        pricing -> Postgres++ : get all models
        return model configurations
    else If no any model
        pricing -> EventBridge : return 500 error
    end

    ' -- Configurations --
    pricing -> Postgres++ : get time configs
    return static time configs

    pricing -> Postgres++ : get region configs
    return static region configs

    ' -- Validation --
    opt#LightBlue Input Validation
        alt missing demand data
            pricing -> EventBridge : return 500 error
        end

        note right pricing
            <b>Required Request Fields Mapping:</b>
            - STATIC_TIME_BASED_CONFIGURATION
            - STATIC_REGION_BASED_CONFIGURATION
            - LIVE_STANDARD_INPUT
        end note
        alt missing any request fields mapping
            pricing -> EventBridge : return 500 error
        end
    end

    pricing -> Redis++ : get fare request count
    return request count data

    ' -- Surge Processing --
    opt#LightBlue Process Http Request
        loop for each model
            pricing -> pricing : build model request
            pricing -> surge++ : compute surge
            return surge results

            opt #LightBlue Storage
                pricing -> Postgres++ : store log request
                pricing -> Postgres++ : store surge results
                pricing -> Redis++ : cache results
            end
        end
    end
end


' ========== FINAL RESPONSE ==========
pricing -> EventBridge : return 204 (Success)
deactivate pricing

@enduml